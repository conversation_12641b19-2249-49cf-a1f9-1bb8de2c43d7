// Shader GLSL per rendering volumetrico avanzato delle figure
const VolumetricShader = {
    vertexShader: `
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec2 vUv;
        varying vec3 vWorldPosition;
        varying vec3 vViewPosition;
        
        uniform float time;
        uniform float morphFactor;
        uniform vec3 lightPosition;
        
        // Noise functions per deformazioni procedurali
        vec3 mod289(vec3 x) {
            return x - floor(x * (1.0 / 289.0)) * 289.0;
        }
        
        vec4 mod289(vec4 x) {
            return x - floor(x * (1.0 / 289.0)) * 289.0;
        }
        
        vec4 permute(vec4 x) {
            return mod289(((x*34.0)+1.0)*x);
        }
        
        vec4 taylorInvSqrt(vec4 r) {
            return 1.79284291400159 - 0.85373472095314 * r;
        }
        
        float snoise(vec3 v) {
            const vec2 C = vec2(1.0/6.0, 1.0/3.0);
            const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);
            
            vec3 i = floor(v + dot(v, C.yyy));
            vec3 x0 = v - i + dot(i, C.xxx);
            
            vec3 g = step(x0.yzx, x0.xyz);
            vec3 l = 1.0 - g;
            vec3 i1 = min(g.xyz, l.zxy);
            vec3 i2 = max(g.xyz, l.zxy);
            
            vec3 x1 = x0 - i1 + C.xxx;
            vec3 x2 = x0 - i2 + C.yyy;
            vec3 x3 = x0 - D.yyy;
            
            i = mod289(i);
            vec4 p = permute(permute(permute(
                i.z + vec4(0.0, i1.z, i2.z, 1.0))
                + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                + i.x + vec4(0.0, i1.x, i2.x, 1.0));
                
            float n_ = 0.142857142857;
            vec3 ns = n_ * D.wyz - D.xzx;
            
            vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
            
            vec4 x_ = floor(j * ns.z);
            vec4 y_ = floor(j - 7.0 * x_);
            
            vec4 x = x_ *ns.x + ns.yyyy;
            vec4 y = y_ *ns.x + ns.yyyy;
            vec4 h = 1.0 - abs(x) - abs(y);
            
            vec4 b0 = vec4(x.xy, y.xy);
            vec4 b1 = vec4(x.zw, y.zw);
            
            vec4 s0 = floor(b0)*2.0 + 1.0;
            vec4 s1 = floor(b1)*2.0 + 1.0;
            vec4 sh = -step(h, vec4(0.0));
            
            vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
            vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;
            
            vec3 p0 = vec3(a0.xy, h.x);
            vec3 p1 = vec3(a0.zw, h.y);
            vec3 p2 = vec3(a1.xy, h.z);
            vec3 p3 = vec3(a1.zw, h.w);
            
            vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
            p0 *= norm.x;
            p1 *= norm.y;
            p2 *= norm.z;
            p3 *= norm.w;
            
            vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
            m = m * m;
            return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
        }
        
        void main() {
            vUv = uv;
            vNormal = normalize(normalMatrix * normal);
            
            // Deformazione procedurale basata su noise
            vec3 pos = position;
            float noiseScale = 0.5;
            float noiseStrength = 0.1 * morphFactor;
            
            // Multipli livelli di noise per dettagli organici
            float noise1 = snoise(pos * noiseScale + time * 0.1);
            float noise2 = snoise(pos * noiseScale * 2.0 + time * 0.05) * 0.5;
            float noise3 = snoise(pos * noiseScale * 4.0 + time * 0.02) * 0.25;
            
            vec3 displacement = normal * (noise1 + noise2 + noise3) * noiseStrength;
            pos += displacement;
            
            // Breathing effect per dare vita alle figure
            float breathe = sin(time * 2.0) * 0.02;
            pos += normal * breathe;
            
            vPosition = pos;
            vWorldPosition = (modelMatrix * vec4(pos, 1.0)).xyz;
            
            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
            vViewPosition = mvPosition.xyz;
            
            gl_Position = projectionMatrix * mvPosition;
        }
    `,
    
    fragmentShader: `
        uniform float time;
        uniform vec3 lightPosition;
        uniform vec3 lightColor;
        uniform float lightIntensity;
        uniform vec3 materialColor;
        uniform float roughness;
        uniform float metalness;
        uniform float subsurface;
        uniform sampler2D noiseTexture;
        
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec2 vUv;
        varying vec3 vWorldPosition;
        varying vec3 vViewPosition;
        
        // Funzioni per PBR (Physically Based Rendering)
        vec3 fresnelSchlick(float cosTheta, vec3 F0) {
            return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
        }
        
        float distributionGGX(vec3 N, vec3 H, float roughness) {
            float a = roughness * roughness;
            float a2 = a * a;
            float NdotH = max(dot(N, H), 0.0);
            float NdotH2 = NdotH * NdotH;
            
            float num = a2;
            float denom = (NdotH2 * (a2 - 1.0) + 1.0);
            denom = 3.14159265 * denom * denom;
            
            return num / denom;
        }
        
        float geometrySchlickGGX(float NdotV, float roughness) {
            float r = (roughness + 1.0);
            float k = (r * r) / 8.0;
            
            float num = NdotV;
            float denom = NdotV * (1.0 - k) + k;
            
            return num / denom;
        }
        
        float geometrySmith(vec3 N, vec3 V, vec3 L, float roughness) {
            float NdotV = max(dot(N, V), 0.0);
            float NdotL = max(dot(N, L), 0.0);
            float ggx2 = geometrySchlickGGX(NdotV, roughness);
            float ggx1 = geometrySchlickGGX(NdotL, roughness);
            
            return ggx1 * ggx2;
        }
        
        // Subsurface scattering per effetto pelle
        vec3 subsurfaceScattering(vec3 lightDir, vec3 normal, vec3 viewDir, float thickness) {
            vec3 scatterDir = lightDir + normal * 0.3;
            float scatter = pow(clamp(dot(viewDir, -scatterDir), 0.0, 1.0), 4.0) * thickness;
            return vec3(1.0, 0.4, 0.2) * scatter;
        }
        
        void main() {
            vec3 normal = normalize(vNormal);
            vec3 viewDir = normalize(-vViewPosition);
            vec3 lightDir = normalize(lightPosition - vWorldPosition);
            vec3 halfwayDir = normalize(lightDir + viewDir);
            
            // Calcolo distanza luce per attenuazione
            float lightDistance = length(lightPosition - vWorldPosition);
            float attenuation = 1.0 / (1.0 + 0.09 * lightDistance + 0.032 * lightDistance * lightDistance);
            
            // Colore base con variazioni procedurali
            vec3 albedo = materialColor;
            float noiseValue = texture2D(noiseTexture, vUv * 2.0 + time * 0.01).r;
            albedo = mix(albedo, albedo * 1.2, noiseValue * 0.3);
            
            // PBR calculation
            vec3 F0 = vec3(0.04);
            F0 = mix(F0, albedo, metalness);
            
            vec3 F = fresnelSchlick(max(dot(halfwayDir, viewDir), 0.0), F0);
            float NDF = distributionGGX(normal, halfwayDir, roughness);
            float G = geometrySmith(normal, viewDir, lightDir, roughness);
            
            vec3 numerator = NDF * G * F;
            float denominator = 4.0 * max(dot(normal, viewDir), 0.0) * max(dot(normal, lightDir), 0.0);
            vec3 specular = numerator / max(denominator, 0.001);
            
            vec3 kS = F;
            vec3 kD = vec3(1.0) - kS;
            kD *= 1.0 - metalness;
            
            float NdotL = max(dot(normal, lightDir), 0.0);
            
            // Subsurface scattering per realismo
            vec3 subsurface_contrib = subsurfaceScattering(lightDir, normal, viewDir, subsurface);
            
            vec3 Lo = (kD * albedo / 3.14159265 + specular + subsurface_contrib) * lightColor * lightIntensity * NdotL * attenuation;
            
            // Ambient lighting
            vec3 ambient = vec3(0.03) * albedo;
            
            vec3 color = ambient + Lo;
            
            // Tone mapping e gamma correction
            color = color / (color + vec3(1.0));
            color = pow(color, vec3(1.0/2.2));
            
            gl_FragColor = vec4(color, 1.0);
        }
    `
};
