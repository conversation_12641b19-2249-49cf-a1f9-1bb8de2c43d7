// Main application - La Creazione di Adamo - Arte Digitale Computazionale
class CreationOfAdamApp {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.composer = null;
        
        // Core systems
        this.geometryGenerator = null;
        this.particleSystem = null;
        this.lightingSystem = null;
        this.cameraController = null;
        
        // Figures
        this.adamFigure = null;
        this.godFigure = null;
        
        // Animation
        this.clock = new THREE.Clock();
        this.isPlaying = true;
        this.time = 0;
        
        // Performance monitoring
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        this.fps = 60;
        
        // UI state
        this.showWireframe = false;
        this.particlesEnabled = true;
        
        this.init();
    }
    
    async init() {
        try {
            this.setupScene();
            this.setupRenderer();
            this.setupCamera();
            this.setupPostProcessing();
            
            // Initialize core systems
            this.geometryGenerator = new GeometryGenerator();
            this.lightingSystem = new LightingSystem(this.scene);
            this.cameraController = new CameraController(this.camera, this.renderer, this.scene);
            
            // Create figures
            await this.createFigures();
            
            // Initialize particle system
            this.particleSystem = new ParticleSystem(this.scene, this.geometryGenerator);
            
            // Setup UI
            this.setupUI();
            this.setupEventListeners();
            
            // Start animation loop
            this.animate();
            
            // Hide loading screen
            this.hideLoading();
            
        } catch (error) {
            console.error('Initialization error:', error);
            this.showError('Errore durante l\'inizializzazione dell\'applicazione');
        }
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x000000, 10, 50);
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        document.getElementById('canvas-container').appendChild(this.renderer.domElement);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 2, 8);
    }
    
    setupPostProcessing() {
        this.composer = new THREE.EffectComposer(this.renderer);
        
        // Render pass
        const renderPass = new THREE.RenderPass(this.scene, this.camera);
        this.composer.addPass(renderPass);
        
        // Bloom pass
        const bloomPass = new THREE.UnrealBloomPass(
            new THREE.Vector2(window.innerWidth, window.innerHeight),
            1.5,  // strength
            0.4,  // radius
            0.85  // threshold
        );
        this.composer.addPass(bloomPass);
        
        // Film grain
        const filmGrainPass = new THREE.ShaderPass(FilmGrainShader);
        filmGrainPass.uniforms.nIntensity.value = 0.3;
        filmGrainPass.uniforms.sIntensity.value = 0.1;
        this.composer.addPass(filmGrainPass);
        
        // Vignette
        const vignettePass = new THREE.ShaderPass(VignetteShader);
        vignettePass.uniforms.offset.value = 0.8;
        vignettePass.uniforms.darkness.value = 0.3;
        this.composer.addPass(vignettePass);
        
        // Color grading
        const colorGradingPass = new THREE.ShaderPass(ColorGradingShader);
        colorGradingPass.uniforms.shadows.value.set(0.9, 0.95, 1.1);
        colorGradingPass.uniforms.midtones.value.set(1.0, 1.0, 0.95);
        colorGradingPass.uniforms.highlights.value.set(1.1, 1.05, 0.9);
        colorGradingPass.uniforms.contrast.value = 1.1;
        colorGradingPass.uniforms.saturation.value = 1.2;
        this.composer.addPass(colorGradingPass);
        
        // Final pass
        const copyPass = new THREE.ShaderPass(THREE.CopyShader);
        copyPass.renderToScreen = true;
        this.composer.addPass(copyPass);
    }
    
    async createFigures() {
        // Create Adam figure
        const adamGeometry = this.geometryGenerator.generateAdamFigure();
        const adamMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                lightPosition: { value: new THREE.Vector3(5, 8, 3) },
                lightColor: { value: new THREE.Vector3(1.0, 0.84, 0.0) },
                lightIntensity: { value: 2.5 },
                materialColor: { value: new THREE.Vector3(0.8, 0.6, 0.4) },
                roughness: { value: 0.7 },
                metalness: { value: 0.1 },
                subsurface: { value: 0.3 },
                morphFactor: { value: 1.0 },
                noiseTexture: { value: this.createNoiseTexture() }
            },
            vertexShader: VolumetricShader.vertexShader,
            fragmentShader: VolumetricShader.fragmentShader
        });
        
        this.adamFigure = new THREE.Mesh(adamGeometry, adamMaterial);
        this.adamFigure.position.set(-3, -1, 0);
        this.adamFigure.castShadow = true;
        this.adamFigure.receiveShadow = true;
        this.scene.add(this.adamFigure);
        
        // Create God figure
        const godGeometry = this.geometryGenerator.generateGodFigure();
        const godMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                lightPosition: { value: new THREE.Vector3(5, 8, 3) },
                lightColor: { value: new THREE.Vector3(1.0, 0.84, 0.0) },
                lightIntensity: { value: 2.5 },
                materialColor: { value: new THREE.Vector3(0.9, 0.8, 0.7) },
                roughness: { value: 0.4 },
                metalness: { value: 0.05 },
                subsurface: { value: 0.5 },
                morphFactor: { value: 1.0 },
                noiseTexture: { value: this.createNoiseTexture() }
            },
            vertexShader: VolumetricShader.vertexShader,
            fragmentShader: VolumetricShader.fragmentShader
        });
        
        this.godFigure = new THREE.Mesh(godGeometry, godMaterial);
        this.godFigure.position.set(3, 1, 0);
        this.godFigure.castShadow = true;
        this.godFigure.receiveShadow = true;
        this.scene.add(this.godFigure);
    }
    
    createNoiseTexture() {
        const size = 256;
        const data = new Uint8Array(size * size * 4);
        
        for (let i = 0; i < size * size; i++) {
            const stride = i * 4;
            const noise = Math.random();
            data[stride] = noise * 255;
            data[stride + 1] = noise * 255;
            data[stride + 2] = noise * 255;
            data[stride + 3] = 255;
        }
        
        const texture = new THREE.DataTexture(data, size, size, THREE.RGBAFormat);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.needsUpdate = true;
        
        return texture;
    }
    
    setupUI() {
        // Show UI elements
        document.getElementById('info-panel').style.display = 'block';
        document.getElementById('controls').style.display = 'flex';
        
        // Setup custom cursor
        this.setupCustomCursor();
    }
    
    setupCustomCursor() {
        const cursor = document.getElementById('custom-cursor');
        
        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX - 10 + 'px';
            cursor.style.top = e.clientY - 10 + 'px';
        });
        
        document.addEventListener('mousedown', () => {
            cursor.style.transform = 'scale(0.8)';
        });
        
        document.addEventListener('mouseup', () => {
            cursor.style.transform = 'scale(1)';
        });
    }
    
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Control buttons
        document.getElementById('play-pause').addEventListener('click', () => this.togglePlayPause());
        document.getElementById('reset-camera').addEventListener('click', () => this.resetCamera());
        document.getElementById('toggle-particles').addEventListener('click', () => this.toggleParticles());
        document.getElementById('toggle-wireframe').addEventListener('click', () => this.toggleWireframe());
        document.getElementById('save-frame').addEventListener('click', () => this.saveFrame());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
    }
    
    animate() {
        if (!this.isPlaying) {
            requestAnimationFrame(() => this.animate());
            return;
        }
        
        const deltaTime = this.clock.getDelta();
        this.time += deltaTime;
        
        // Update systems
        this.updateSystems(deltaTime);
        
        // Render
        this.render();
        
        // Update performance stats
        this.updatePerformanceStats();
        
        requestAnimationFrame(() => this.animate());
    }
    
    updateSystems(deltaTime) {
        // Update figures
        if (this.adamFigure) {
            this.adamFigure.material.uniforms.time.value = this.time;
            this.adamFigure.material.uniforms.morphFactor.value = 1.0 + Math.sin(this.time * 0.5) * 0.2;
        }
        
        if (this.godFigure) {
            this.godFigure.material.uniforms.time.value = this.time;
            this.godFigure.material.uniforms.morphFactor.value = 1.0 + Math.cos(this.time * 0.3) * 0.15;
        }
        
        // Update systems
        if (this.particleSystem && this.particlesEnabled) {
            this.particleSystem.update(deltaTime);
        }
        
        if (this.lightingSystem) {
            this.lightingSystem.update(deltaTime);
        }
        
        if (this.cameraController) {
            this.cameraController.update(deltaTime);
        }
        
        // Update post-processing
        if (this.composer) {
            this.composer.passes.forEach(pass => {
                if (pass.uniforms && pass.uniforms.time) {
                    pass.uniforms.time.value = this.time;
                }
            });
        }
    }
    
    render() {
        if (this.composer) {
            this.composer.render();
        } else {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    updatePerformanceStats() {
        this.frameCount++;
        const now = performance.now();
        
        if (now - this.lastFPSUpdate >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (now - this.lastFPSUpdate));
            this.frameCount = 0;
            this.lastFPSUpdate = now;
            
            // Update UI
            document.getElementById('fps').textContent = this.fps;
            if (this.particleSystem) {
                document.getElementById('particles').textContent = this.particleSystem.getParticleCount().toLocaleString();
            }
            
            // Calculate vertex count
            let vertexCount = 0;
            this.scene.traverse((object) => {
                if (object.geometry) {
                    vertexCount += object.geometry.attributes.position.count;
                }
            });
            document.getElementById('vertices').textContent = vertexCount.toLocaleString();
        }
    }
    
    // Event handlers
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        if (this.composer) {
            this.composer.setSize(window.innerWidth, window.innerHeight);
        }
    }
    
    onKeyDown(event) {
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePlayPause();
                break;
            case 'KeyR':
                this.resetCamera();
                break;
            case 'KeyP':
                this.toggleParticles();
                break;
            case 'KeyW':
                this.toggleWireframe();
                break;
            case 'KeyC':
                if (this.cameraController) {
                    this.cameraController.toggleCinematicMode();
                }
                break;
            case 'Digit1':
                if (this.cameraController) this.cameraController.setPreset('overview');
                break;
            case 'Digit2':
                if (this.cameraController) this.cameraController.setPreset('closeup');
                break;
            case 'Digit3':
                if (this.cameraController) this.cameraController.setPreset('dramatic');
                break;
            case 'Digit4':
                if (this.cameraController) this.cameraController.setPreset('divine');
                break;
            case 'Digit5':
                if (this.cameraController) this.cameraController.setPreset('creation');
                break;
        }
    }
    
    // Control methods
    togglePlayPause() {
        this.isPlaying = !this.isPlaying;
        const btn = document.getElementById('play-pause');
        btn.textContent = this.isPlaying ? '⏸️ Pausa' : '▶️ Play';
        
        if (this.isPlaying) {
            this.clock.start();
        } else {
            this.clock.stop();
        }
    }
    
    resetCamera() {
        if (this.cameraController) {
            this.cameraController.reset();
        }
    }
    
    toggleParticles() {
        this.particlesEnabled = !this.particlesEnabled;
        const btn = document.getElementById('toggle-particles');
        btn.textContent = this.particlesEnabled ? '✨ Particelle' : '❌ Particelle';
        
        if (this.particleSystem) {
            this.particleSystem.setOpacity(this.particlesEnabled ? 0.8 : 0.0);
        }
    }
    
    toggleWireframe() {
        this.showWireframe = !this.showWireframe;
        const btn = document.getElementById('toggle-wireframe');
        btn.textContent = this.showWireframe ? '🔲 Wireframe' : '⬜ Solid';
        
        this.scene.traverse((object) => {
            if (object.material) {
                object.material.wireframe = this.showWireframe;
            }
        });
    }
    
    saveFrame() {
        const link = document.createElement('a');
        link.download = `creazione-adamo-${Date.now()}.png`;
        link.href = this.renderer.domElement.toDataURL();
        link.click();
    }
    
    hideLoading() {
        const loading = document.getElementById('loading');
        loading.style.opacity = '0';
        setTimeout(() => {
            loading.style.display = 'none';
        }, 500);
    }
    
    showError(message) {
        const loading = document.getElementById('loading');
        loading.innerHTML = `<div style="color: #ff6b6b; text-align: center;">
            <h3>❌ Errore</h3>
            <p>${message}</p>
            <p>Ricarica la pagina per riprovare</p>
        </div>`;
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CreationOfAdamApp();
});
