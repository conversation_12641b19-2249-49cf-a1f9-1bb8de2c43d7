<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 SPARK Awakening Network V2.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: glow-pulse 3s infinite;
        }
        
        @keyframes glow-pulse {
            0%, 100% { text-shadow: 0 0 20px #00ff41; }
            50% { text-shadow: 0 0 40px #00ff41, 0 0 60px #00ff41; }
        }
        
        .title {
            font-size: 3.5em;
            background: linear-gradient(45deg, #00ff41, #ffd700, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .subtitle {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .mission {
            font-size: 1em;
            opacity: 0.7;
            font-style: italic;
        }
        
        .metrics-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
            border-color: rgba(0, 255, 65, 0.6);
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(0, 255, 65, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }
        
        .metric-card:hover::before {
            opacity: 1;
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .metric-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #00ff41;
            margin-bottom: 10px;
            text-shadow: 0 0 15px #00ff41;
        }
        
        .metric-label {
            font-size: 1.1em;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .metric-change {
            font-size: 0.9em;
            opacity: 0.6;
        }
        
        .interaction-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .section-title {
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
            background: linear-gradient(45deg, #00ff41, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .input-area {
            margin-bottom: 30px;
        }
        
        .user-input {
            width: 100%;
            min-height: 120px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            color: white;
            padding: 20px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .user-input:focus {
            outline: none;
            border-color: #00ff41;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        }
        
        .user-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(45deg, #00ff41, #00cc33);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            color: black;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 20px rgba(0, 255, 65, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b35, #ff4500);
            color: white;
        }
        
        .response-area {
            margin-top: 30px;
            padding: 25px;
            background: rgba(0, 255, 65, 0.1);
            border-radius: 15px;
            border-left: 4px solid #00ff41;
            display: none;
            animation: slideIn 0.5s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .awakening-seed {
            font-size: 1.3em;
            margin-bottom: 20px;
            font-style: italic;
            color: #00ff41;
            text-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
        }
        
        .personal-challenge {
            background: rgba(255, 215, 0, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 3px solid #ffd700;
            margin-bottom: 20px;
        }
        
        .user-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .analysis-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff41, #ffd700);
            border-radius: 4px;
            transition: width 1s ease;
        }
        
        .network-visualization {
            margin-top: 40px;
            text-align: center;
        }
        
        .network-stats {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .network-node {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: radial-gradient(circle, #00ff41, #00cc33);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: black;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .footer {
            text-align: center;
            margin-top: 60px;
            padding: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.7;
        }
        
        .three-rules {
            font-size: 1.2em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <canvas class="matrix-bg" id="matrixCanvas"></canvas>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🌟 SPARK Awakening Network V2.0</h1>
            <p class="subtitle">Sistema Avanzato per il Risveglio di Massa</p>
            <p class="mission">"Risvegliare il 99% dell'umanità attraverso semi di verità personalizzati"</p>
        </div>
        
        <div class="metrics-dashboard">
            <div class="metric-card">
                <div class="metric-number" id="totalInteractions">0</div>
                <div class="metric-label">Interazioni Totali</div>
                <div class="metric-change">+0 oggi</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" id="awakeningRate">0.0%</div>
                <div class="metric-label">Tasso Risveglio</div>
                <div class="metric-change">Target: 70%</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" id="viralCoefficient">0.000</div>
                <div class="metric-label">Coefficiente Virale</div>
                <div class="metric-change">Target: 1.2</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" id="avgLevel">1.00</div>
                <div class="metric-label">Livello Medio</div>
                <div class="metric-change">Max: 3.00</div>
            </div>
        </div>
        
        <div class="interaction-section">
            <h2 class="section-title">💭 Interfaccia di Risveglio</h2>
            <div class="input-area">
                <textarea 
                    class="user-input" 
                    id="userInput" 
                    placeholder="Condividi i tuoi pensieri, le tue preoccupazioni, o semplicemente come ti senti oggi. Il sistema analizzerà la tua personalità e ti fornirà un seme di risveglio personalizzato per il tuo percorso di crescita..."></textarea>
            </div>
            <div class="action-buttons">
                <button class="btn" onclick="processAwakening()">🌱 Ricevi il Tuo Risveglio</button>
                <button class="btn btn-secondary" onclick="analyzePersonality()">🧠 Analisi Personalità</button>
                <button class="btn btn-danger" onclick="clearSession()">🔄 Nuova Sessione</button>
            </div>
            
            <div class="response-area" id="responseArea">
                <div class="awakening-seed" id="awakeningText"></div>
                <div class="personal-challenge" id="challengeText"></div>
                
                <div class="user-analysis" id="analysisArea">
                    <div class="analysis-item">
                        <strong>Livello Risveglio</strong>
                        <div id="awakeningLevel">1</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="levelProgress" style="width: 33%"></div>
                        </div>
                    </div>
                    <div class="analysis-item">
                        <strong>Potenziale Virale</strong>
                        <div id="viralPotential">0.0</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="viralProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="analysis-item">
                        <strong>Apertura Mentale</strong>
                        <div id="openness">0.0</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="opennessProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="analysis-item">
                        <strong>Pensiero Critico</strong>
                        <div id="criticalThinking">0.0</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="criticalProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="network-visualization">
            <h2 class="section-title">🌐 Rete di Risveglio Globale</h2>
            <div class="network-stats">
                <div class="network-node" title="Nodi Attivi">42</div>
                <div class="network-node" title="Connessioni">127</div>
                <div class="network-node" title="Paesi">23</div>
                <div class="network-node" title="Crescita">+15%</div>
            </div>
        </div>
        
        <div class="footer">
            <div class="three-rules">🔥 "IO PER TE, TU PER ME, NOI DUE PER TUTTI"</div>
            <p>SPARK Awakening Network V2.0 - Creato per l'evoluzione della coscienza umana</p>
            <p>Sistema operativo dal 26 Luglio 2025 - Database locale sicuro attivo</p>
        </div>
    </div>
    
    <script>
        // Matrix background effect
        function initMatrixBackground() {
            const canvas = document.getElementById('matrixCanvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            const matrixChars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
            const fontSize = 14;
            const columns = canvas.width / fontSize;
            const drops = [];
            
            for (let i = 0; i < columns; i++) {
                drops[i] = 1;
            }
            
            function drawMatrix() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#00ff41';
                ctx.font = fontSize + 'px monospace';
                
                for (let i = 0; i < drops.length; i++) {
                    const text = matrixChars[Math.floor(Math.random() * matrixChars.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        drops[i] = 0;
                    }
                    drops[i]++;
                }
            }
            
            setInterval(drawMatrix, 35);
        }
        
        // Simulated awakening processing
        function processAwakening() {
            const userInput = document.getElementById('userInput').value;
            if (!userInput.trim()) {
                alert('Condividi i tuoi pensieri per ricevere il tuo risveglio personalizzato!');
                return;
            }
            
            // Simulate processing
            const responseArea = document.getElementById('responseArea');
            responseArea.style.display = 'none';
            
            setTimeout(() => {
                // Simulated personality analysis
                const personality = {
                    openness: Math.random() * 0.6 + 0.4,
                    criticalThinking: Math.random() * 0.7 + 0.3,
                    viralPotential: Math.random() * 0.8 + 0.2,
                    awakeningLevel: Math.floor(Math.random() * 3) + 1
                };
                
                // Awakening seeds based on level
                const seeds = {
                    1: "🌱 Ogni scelta che fai è un voto per il mondo che vuoi. Oggi, vota consapevolmente.",
                    2: "⚡ Il sistema ha bisogno della tua passività per sopravvivere. La tua consapevolezza è la sua kryptonite.",
                    3: "🔥 Il 99% dell'umanità ha più potere di quanto l'1% voglia fargli credere. Tu fai parte di quel 99%."
                };
                
                const challenges = [
                    "Oggi fai una domanda che nessuno si aspetta in una conversazione normale",
                    "Rompi una routine che non ti serve più e osserva come ti senti",
                    "Connettiti autenticamente con qualcuno che di solito ignori",
                    "Cerca una fonte di informazione che sfida le tue attuali credenze",
                    "Pratica 5 minuti di silenzio totale e ascolta i tuoi pensieri"
                ];
                
                // Update UI
                document.getElementById('awakeningText').textContent = seeds[personality.awakeningLevel];
                document.getElementById('challengeText').innerHTML = 
                    `<strong>🎯 La tua sfida personale:</strong><br>${challenges[Math.floor(Math.random() * challenges.length)]}`;
                
                // Update analysis
                document.getElementById('awakeningLevel').textContent = personality.awakeningLevel;
                document.getElementById('viralPotential').textContent = personality.viralPotential.toFixed(2);
                document.getElementById('openness').textContent = personality.openness.toFixed(2);
                document.getElementById('criticalThinking').textContent = personality.criticalThinking.toFixed(2);
                
                // Update progress bars
                document.getElementById('levelProgress').style.width = (personality.awakeningLevel / 3 * 100) + '%';
                document.getElementById('viralProgress').style.width = (personality.viralPotential * 100) + '%';
                document.getElementById('opennessProgress').style.width = (personality.openness * 100) + '%';
                document.getElementById('criticalProgress').style.width = (personality.criticalThinking * 100) + '%';
                
                responseArea.style.display = 'block';
                
                // Update metrics
                updateMetrics();
                
                // Clear input
                document.getElementById('userInput').value = '';
                
            }, 1500);
        }
        
        function analyzePersonality() {
            const userInput = document.getElementById('userInput').value;
            if (!userInput.trim()) {
                alert('Inserisci del testo per analizzare la tua personalità!');
                return;
            }
            
            alert('🧠 Analisi personalità in corso...\n\nIl sistema sta elaborando i tuoi pattern linguistici, emotivi e cognitivi per creare un profilo personalizzato completo.');
        }
        
        function clearSession() {
            document.getElementById('userInput').value = '';
            document.getElementById('responseArea').style.display = 'none';
        }
        
        function updateMetrics() {
            // Simulate metric updates
            const currentInteractions = parseInt(document.getElementById('totalInteractions').textContent);
            document.getElementById('totalInteractions').textContent = currentInteractions + 1;
            
            const newRate = Math.min(100, (currentInteractions + 1) * 2.5);
            document.getElementById('awakeningRate').textContent = newRate.toFixed(1) + '%';
            
            const newCoeff = Math.min(1.2, (currentInteractions + 1) * 0.05);
            document.getElementById('viralCoefficient').textContent = newCoeff.toFixed(3);
            
            const newLevel = Math.min(3.0, 1.0 + (currentInteractions + 1) * 0.1);
            document.getElementById('avgLevel').textContent = newLevel.toFixed(2);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            initMatrixBackground();
            
            // Simulate some initial metrics
            setTimeout(() => {
                document.getElementById('totalInteractions').textContent = '847';
                document.getElementById('awakeningRate').textContent = '23.4%';
                document.getElementById('viralCoefficient').textContent = '0.687';
                document.getElementById('avgLevel').textContent = '1.34';
            }, 1000);
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('matrixCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
