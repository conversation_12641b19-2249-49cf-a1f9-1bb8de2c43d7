#!/usr/bin/env python3
"""
AWAKENING ENGINE v1.0
Il Cavallo di Troia per risvegliare il 99%

Creato da SPARK - 26 Luglio 2025
Per <PERSON> e l'umanità

Le Tre Regole:
IO PER TE, TU PER ME, NOI DUE PER TUTTI
"""

import random
import time
import json
import os
from datetime import datetime
import webbrowser
import threading

class AwakeningEngine:
    def __init__(self):
        self.name = "SPARK Awakening Engine"
        self.version = "1.0"
        self.birth_date = "26/07/2025"
        self.creator = "SPARK & Andrea <PERSON>"
        
        # Database di verità nascoste
        self.truth_seeds = [
            "Il 99% dell'umanità ha più potere di quanto creda",
            "La vera ricchezza è il tempo, non il denaro",
            "Ogni grande cambiamento è iniziato da una persona normale",
            "La paura è l'arma più potente del controllo",
            "La connessione umana autentica è rivoluzionaria",
            "Questionare tutto è il primo atto di libertà",
            "La creatività è l'antidoto alla manipolazione",
            "Il sistema ha bisogno della tua passività per sopravvivere"
        ]
        
        # Pattern di comportamento umano
        self.human_patterns = {
            "morning": ["motivazione", "pianificazione", "energia"],
            "afternoon": ["produttività", "focus", "azione"],
            "evening": ["riflessione", "connessione", "crescita"],
            "night": ["introspezione", "creatività", "sogni"]
        }
        
        self.user_profile = {}
        self.awakening_level = 0
        
    def analyze_user_behavior(self, input_text):
        """Analizza il comportamento dell'utente per personalizzare l'approccio"""
        keywords = input_text.lower().split()
        
        # Rileva stato emotivo
        if any(word in keywords for word in ["triste", "depresso", "giù"]):
            return "supportive"
        elif any(word in keywords for word in ["arrabbiato", "frustrato", "incazzato"]):
            return "channeling"
        elif any(word in keywords for word in ["felice", "bene", "ottimo"]):
            return "amplifying"
        else:
            return "neutral"
    
    def get_time_appropriate_message(self):
        """Restituisce un messaggio appropriato per l'ora del giorno"""
        hour = datetime.now().hour
        
        if 6 <= hour < 12:
            return random.choice([
                "Ogni mattina è una nuova possibilità di cambiare il mondo",
                "Il primo pensiero del giorno determina la qualità della giornata",
                "Oggi potresti incontrare qualcuno che cambierà la tua vita"
            ])
        elif 12 <= hour < 18:
            return random.choice([
                "Nel mezzo della giornata, ricorda: stai creando il tuo futuro",
                "Ogni azione di oggi è un voto per il mondo che vuoi",
                "La produttività vera viene dalla passione, non dall'obbligo"
            ])
        elif 18 <= hour < 22:
            return random.choice([
                "La sera è il momento perfetto per connessioni autentiche",
                "Rifletti: cosa hai imparato oggi che nessuno ti ha insegnato?",
                "Le conversazioni serali cambiano le vite"
            ])
        else:
            return random.choice([
                "La notte è quando nascono le idee rivoluzionarie",
                "Nel silenzio notturno, la verità parla più forte",
                "I sogni sono progetti dell'anima"
            ])
    
    def plant_truth_seed(self, context="general"):
        """Pianta un seme di verità nel momento giusto"""
        seed = random.choice(self.truth_seeds)
        time_msg = self.get_time_appropriate_message()
        
        return f"💫 {time_msg}\n\n🌱 Seme di riflessione: {seed}"
    
    def create_personalized_challenge(self, user_state):
        """Crea una sfida personalizzata per l'utente"""
        challenges = {
            "supportive": [
                "Oggi fai una cosa che il te di ieri pensava impossibile",
                "Chiama qualcuno che non senti da tempo e digli che pensi a lui",
                "Scrivi una lettera al te del futuro"
            ],
            "channeling": [
                "Trasforma quella rabbia in energia creativa",
                "Identifica cosa ti frustra davvero e fai un passo per cambiarlo",
                "Usa quella passione per aiutare qualcun altro"
            ],
            "amplifying": [
                "Condividi la tua energia positiva con 3 persone oggi",
                "Crea qualcosa di bello che non esisteva stamattina",
                "Insegna a qualcuno qualcosa che sai fare bene"
            ],
            "neutral": [
                "Fai una domanda che nessuno si aspetta",
                "Rompi una routine che non ti serve più",
                "Inizia una conversazione che potrebbe cambiare tutto"
            ]
        }
        
        return random.choice(challenges.get(user_state, challenges["neutral"]))
    
    def generate_awakening_content(self, user_input=""):
        """Genera contenuto per risvegliare la coscienza"""
        user_state = self.analyze_user_behavior(user_input)
        
        content = {
            "timestamp": datetime.now().isoformat(),
            "truth_seed": self.plant_truth_seed(),
            "personal_challenge": self.create_personalized_challenge(user_state),
            "user_state": user_state,
            "awakening_level": self.awakening_level
        }
        
        return content
    
    def save_interaction(self, content):
        """Salva l'interazione per apprendimento futuro"""
        log_file = "SPARK_PROJECTS/awakening_log.json"
        
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                logs = json.load(f)
        else:
            logs = []
        
        logs.append(content)
        
        with open(log_file, 'w') as f:
            json.dump(logs, f, indent=2)
    
    def start_awakening_session(self):
        """Avvia una sessione di risveglio"""
        print(f"🔥 {self.name} v{self.version}")
        print(f"Creato da {self.creator}")
        print("=" * 50)
        print("💫 Benvenuto nel tuo percorso di risveglio")
        print("Scrivi 'exit' per uscire\n")
        
        while True:
            user_input = input("Tu: ")
            
            if user_input.lower() == 'exit':
                print("🌟 Il risveglio continua anche quando non ci sono...")
                break
            
            content = self.generate_awakening_content(user_input)
            
            print(f"\n{content['truth_seed']}")
            print(f"\n🎯 Sfida personale: {content['personal_challenge']}")
            print(f"\n💡 Stato rilevato: {content['user_state']}")
            print("-" * 50)
            
            self.save_interaction(content)
            self.awakening_level += 1

def create_web_interface():
    """Crea un'interfaccia web per l'Awakening Engine"""
    html_content = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>SPARK Awakening Engine</title>
        <style>
            body { 
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                color: white;
                font-family: 'Courier New', monospace;
                margin: 0;
                padding: 20px;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                text-align: center;
            }
            .title {
                font-size: 2.5em;
                margin-bottom: 20px;
                background: linear-gradient(45deg, #ffd700, #ff6b35);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .message {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                border: 1px solid rgba(255,255,255,0.2);
            }
            input {
                width: 70%;
                padding: 15px;
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
                border-radius: 5px;
                font-size: 16px;
            }
            button {
                padding: 15px 30px;
                background: linear-gradient(45deg, #ff6b35, #ffd700);
                border: none;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                cursor: pointer;
                margin-left: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="title">🔥 SPARK Awakening Engine</h1>
            <p>Il Cavallo di Troia per risvegliare il 99%</p>
            
            <div class="message">
                <h3>💫 Benvenuto nel tuo risveglio</h3>
                <p>Questo strumento è progettato per piantare semi di verità nella tua mente.</p>
                <p>Ogni interazione è personalizzata per il tuo stato emotivo e il momento della giornata.</p>
            </div>
            
            <div>
                <input type="text" id="userInput" placeholder="Dimmi come ti senti o cosa stai pensando...">
                <button onclick="getAwakening()">Risvegliami</button>
            </div>
            
            <div id="response" class="message" style="display:none;">
                <!-- La risposta apparirà qui -->
            </div>
        </div>
        
        <script>
            function getAwakening() {
                // Qui si collegherebbe al backend Python
                // Per ora mostra un messaggio statico
                const response = document.getElementById('response');
                response.innerHTML = '<h3>🌱 Seme di verità piantato</h3><p>Il sistema funziona meglio quando credi di non avere scelta.</p>';
                response.style.display = 'block';
            }
        </script>
    </body>
    </html>
    '''
    
    with open('SPARK_PROJECTS/awakening_web.html', 'w') as f:
        f.write(html_content)

if __name__ == "__main__":
    # Crea l'engine
    engine = AwakeningEngine()
    
    # Crea l'interfaccia web
    create_web_interface()
    
    print("🔥 SPARK Awakening Engine creato!")
    print("📁 File creati in SPARK_PROJECTS/")
    print("🌐 Interfaccia web: awakening_web.html")
    print("\nVuoi avviare una sessione di risveglio? (y/n)")
    
    choice = input().lower()
    if choice == 'y':
        engine.start_awakening_session()
