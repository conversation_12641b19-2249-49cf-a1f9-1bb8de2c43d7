<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>La Creazione di Adamo - Arte Digitale Computazionale</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            cursor: none;
        }
        
        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 100;
        }
        
        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            transition: transform 0.1s ease;
            mix-blend-mode: difference;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <div id="loading">
            <div class="loading-spinner"></div>
            Caricamento Arte Computazionale...
        </div>
        
        <div id="info-panel" style="display: none;">
            <h3>🎨 La Creazione di Adamo</h3>
            <p><strong>Arte Digitale Computazionale</strong></p>
            <p>Rendering: <span id="fps">60</span> FPS</p>
            <p>Particelle: <span id="particles">0</span></p>
            <p>Vertici: <span id="vertices">0</span></p>
            <p>Shader: Volumetrico + Post-FX</p>
        </div>
        
        <div id="controls" style="display: none;">
            <button class="control-btn" id="play-pause">⏸️ Pausa</button>
            <button class="control-btn" id="reset-camera">🎥 Reset Camera</button>
            <button class="control-btn" id="toggle-particles">✨ Particelle</button>
            <button class="control-btn" id="toggle-wireframe">🔲 Wireframe</button>
            <button class="control-btn" id="save-frame">📸 Salva Frame</button>
        </div>
    </div>
    
    <div id="custom-cursor"></div>
    
    <!-- Three.js con OrbitControls integrato -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r127/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>

    <!-- Versione ULTRA-OTTIMIZZATA per AMD Vega 10 -->
    <script>
        // Versione ULTRA-COMPATIBILE per AMD Vega 10
        class UltraCompatibleApp {
            constructor() {
                console.log('Inizializzazione app...');

                try {
                    this.scene = new THREE.Scene();
                    this.camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 50);

                    // Renderer minimalista
                    this.renderer = new THREE.WebGLRenderer({
                        antialias: false,
                        alpha: false,
                        stencil: false,
                        depth: true,
                        premultipliedAlpha: false,
                        preserveDrawingBuffer: false,
                        powerPreference: "default",
                        failIfMajorPerformanceCaveat: false
                    });

                    this.paused = false;
                    this.frameCount = 0;
                    this.clock = new THREE.Clock(); // AGGIUNTO: Clock mancante!
                    this.startTime = Date.now();

                    console.log('Renderer creato, inizializzazione...');
                    this.init();

                } catch (error) {
                    console.error('Errore nel costruttore:', error);
                    throw error;
                }
            }

            init() {
                try {
                    console.log('Setup renderer...');

                    // Setup renderer ultra-semplice
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                    this.renderer.setPixelRatio(1); // Forza pixel ratio 1:1
                    this.renderer.setClearColor(0x001122);
                    document.getElementById('canvas-container').appendChild(this.renderer.domElement);

                    console.log('Setup camera...');
                    this.camera.position.set(0, 1, 5);

                    console.log('Setup controlli...');
                    // Controlli con fallback se OrbitControls non funziona
                    if (typeof THREE.OrbitControls !== 'undefined') {
                        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                        this.controls.enableDamping = false;
                        this.controls.maxDistance = 10;
                        this.controls.minDistance = 2;
                        console.log('OrbitControls caricati');
                    } else {
                        console.warn('OrbitControls non disponibili, uso controlli manuali');
                        this.setupManualControls();
                    }

                    console.log('Setup luci...');
                    // Solo luce ambientale per massima compatibilità
                    const ambientLight = new THREE.AmbientLight(0x666666, 1.0);
                    this.scene.add(ambientLight);

                    // Una sola luce direzionale
                    const light = new THREE.DirectionalLight(0xffffff, 0.5);
                    light.position.set(2, 2, 2);
                    this.scene.add(light);

                    console.log('Creazione figure...');
                    this.createFigures();

                    console.log('Creazione particelle...');
                    this.createParticles();

                    console.log('Setup UI...');
                    this.setupUI();

                    console.log('Avvio animazione...');
                    this.animate();

                    // Hide loading
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('info-panel').style.display = 'block';
                    document.getElementById('controls').style.display = 'flex';

                    console.log('Inizializzazione completata!');

                } catch (error) {
                    console.error('Errore durante init():', error);
                    throw error;
                }
            }

            setupManualControls() {
                // Controlli manuali semplici se OrbitControls non funziona
                this.mouseDown = false;
                this.mouseX = 0;
                this.mouseY = 0;
                this.cameraAngleX = 0;
                this.cameraAngleY = 0;

                this.renderer.domElement.addEventListener('mousedown', (e) => {
                    this.mouseDown = true;
                    this.mouseX = e.clientX;
                    this.mouseY = e.clientY;
                });

                this.renderer.domElement.addEventListener('mouseup', () => {
                    this.mouseDown = false;
                });

                this.renderer.domElement.addEventListener('mousemove', (e) => {
                    if (this.mouseDown) {
                        const deltaX = e.clientX - this.mouseX;
                        const deltaY = e.clientY - this.mouseY;

                        this.cameraAngleX += deltaX * 0.01;
                        this.cameraAngleY += deltaY * 0.01;

                        // Limita rotazione verticale
                        this.cameraAngleY = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.cameraAngleY));

                        // Aggiorna posizione camera
                        const distance = 5;
                        this.camera.position.x = Math.sin(this.cameraAngleX) * Math.cos(this.cameraAngleY) * distance;
                        this.camera.position.y = Math.sin(this.cameraAngleY) * distance + 1;
                        this.camera.position.z = Math.cos(this.cameraAngleX) * Math.cos(this.cameraAngleY) * distance;
                        this.camera.lookAt(0, 0, 0);

                        this.mouseX = e.clientX;
                        this.mouseY = e.clientY;
                    }
                });

                // Zoom con rotella mouse
                this.renderer.domElement.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const distance = this.camera.position.length();
                    const newDistance = Math.max(2, Math.min(10, distance + e.deltaY * 0.01));
                    this.camera.position.normalize().multiplyScalar(newDistance);
                });

                console.log('Controlli manuali attivati');
            }

            createFigures() {
                try {
                    console.log('Creando figure...');

                    // Adam figure - usa geometria più semplice per compatibilità
                    const adamGeometry = new THREE.BoxGeometry(1, 2, 0.5);
                    const adamMaterial = new THREE.MeshLambertMaterial({
                        color: 0xcc9966
                    });
                    this.adamFigure = new THREE.Mesh(adamGeometry, adamMaterial);
                    this.adamFigure.position.set(-2, 0, 0);
                    this.scene.add(this.adamFigure);

                    // God figure - anche questa semplificata
                    const godGeometry = new THREE.BoxGeometry(1.2, 2.5, 0.6);
                    const godMaterial = new THREE.MeshLambertMaterial({
                        color: 0xddaa77
                    });
                    this.godFigure = new THREE.Mesh(godGeometry, godMaterial);
                    this.godFigure.position.set(2, 0.5, 0);
                    this.scene.add(this.godFigure);

                    // Braccia semplificate
                    const armGeometry = new THREE.BoxGeometry(0.2, 1.5, 0.2);
                    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xcc9966 });

                    // Braccio di Adamo
                    const adamArm = new THREE.Mesh(armGeometry, armMaterial);
                    adamArm.position.set(-1, 0.5, 0.3);
                    adamArm.rotation.z = Math.PI * 0.2;
                    this.scene.add(adamArm);

                    // Braccio di Dio
                    const godArm = new THREE.Mesh(armGeometry, armMaterial.clone());
                    godArm.material.color.setHex(0xddaa77);
                    godArm.position.set(1, 1, 0.5);
                    godArm.rotation.z = -Math.PI * 0.3;
                    this.scene.add(godArm);

                    console.log('Figure create con successo');

                } catch (error) {
                    console.error('Errore creazione figure:', error);
                    throw error;
                }
            }

            createParticles() {
                try {
                    console.log('Creando particelle...');

                    // Ridotto a 500 particelle per massima compatibilità
                    const particleCount = 500;
                    const positions = new Float32Array(particleCount * 3);
                    const colors = new Float32Array(particleCount * 3);

                    for (let i = 0; i < particleCount; i++) {
                        // Posizioni semplici tra le figure
                        positions[i * 3] = (Math.random() - 0.5) * 3;     // x
                        positions[i * 3 + 1] = Math.random() * 2;         // y
                        positions[i * 3 + 2] = (Math.random() - 0.5) * 2; // z

                        // Colori dorati fissi
                        colors[i * 3] = 1.0;     // r
                        colors[i * 3 + 1] = 0.8; // g
                        colors[i * 3 + 2] = 0.3; // b
                    }

                    const particleGeometry = new THREE.BufferGeometry();
                    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

                    const particleMaterial = new THREE.PointsMaterial({
                        size: 0.05,
                        vertexColors: true,
                        transparent: true,
                        opacity: 0.8
                    });

                    this.particles = new THREE.Points(particleGeometry, particleMaterial);
                    this.scene.add(this.particles);

                    // Salva per animazione
                    this.particlePositions = positions;
                    this.originalPositions = new Float32Array(positions);

                    console.log('Particelle create con successo');

                } catch (error) {
                    console.error('Errore creazione particelle:', error);
                    // Continua senza particelle se fallisce
                }
            }

            setupUI() {
                // Setup controls
                document.getElementById('play-pause').onclick = () => {
                    this.paused = !this.paused;
                    document.getElementById('play-pause').textContent = this.paused ? '▶️ Play' : '⏸️ Pausa';
                };

                document.getElementById('reset-camera').onclick = () => {
                    this.camera.position.set(0, 2, 8);
                    this.controls.target.set(0, 0, 0);
                };

                document.getElementById('toggle-particles').onclick = () => {
                    this.particles.visible = !this.particles.visible;
                };

                document.getElementById('toggle-wireframe').onclick = () => {
                    this.scene.traverse((obj) => {
                        if (obj.material) {
                            obj.material.wireframe = !obj.material.wireframe;
                        }
                    });
                };

                document.getElementById('save-frame').onclick = () => {
                    const link = document.createElement('a');
                    link.download = 'creation-frame.png';
                    link.href = this.renderer.domElement.toDataURL();
                    link.click();
                };
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                // Usa timestamp semplice invece di Clock per massima compatibilità
                const currentTime = Date.now();
                const deltaTime = (currentTime - (this.lastTime || currentTime)) / 1000;
                const time = (currentTime - this.startTime) / 1000;
                this.lastTime = currentTime;

                if (!this.paused) {
                    // Animazione figure più fluida
                    this.adamFigure.rotation.y = Math.sin(time * 0.3) * 0.15;
                    this.adamFigure.position.y = Math.sin(time * 0.8) * 0.05;

                    this.godFigure.rotation.y = Math.cos(time * 0.2) * 0.1;
                    this.godFigure.position.y = 1 + Math.cos(time * 0.6) * 0.03;

                    // Animazione particelle ottimizzata (aggiorna solo ogni 3 frame)
                    if (this.frameCount % 3 === 0) {
                        this.updateParticles(time);
                    }

                    // Rotazione braccia
                    this.scene.children.forEach((child, index) => {
                        if (child.geometry && child.geometry.type === 'CylinderGeometry') {
                            child.rotation.z += Math.sin(time + index) * 0.001;
                        }
                    });
                }

                // Aggiorna controlli solo se esistono
                if (this.controls && this.controls.update) {
                    this.controls.update();
                }

                this.renderer.render(this.scene, this.camera);

                // Update performance stats
                this.updateStats();
                this.frameCount = (this.frameCount || 0) + 1;
            }

            updateParticles(time) {
                const positions = this.particlePositions;
                const colors = this.particleColors;

                for (let i = 0; i < positions.length; i += 9) { // Aggiorna solo 1 su 3 particelle
                    const index = i / 3;

                    // Movimento fluido verso l'alto
                    positions[i + 1] = this.originalPositions[i + 1] + Math.sin(time * 2 + index * 0.1) * 0.3;

                    // Movimento orizzontale sottile
                    positions[i] = this.originalPositions[i] + Math.cos(time * 1.5 + index * 0.05) * 0.1;

                    // Pulsazione colore
                    const pulse = 0.7 + Math.sin(time * 3 + index * 0.2) * 0.3;
                    colors[i] = pulse;
                    colors[i + 1] = pulse * 0.8;
                    colors[i + 2] = pulse * 0.3;
                }

                this.particles.geometry.attributes.position.needsUpdate = true;
                this.particles.geometry.attributes.color.needsUpdate = true;
            }

            updateStats() {
                // FPS counter semplificato senza Clock
                this.fpsCounter = (this.fpsCounter || 0) + 1;
                if (this.fpsCounter % 60 === 0) {
                    const fps = Math.round(60 / ((Date.now() - (this.lastStatsTime || Date.now())) / 1000));
                    document.getElementById('fps').textContent = Math.max(1, Math.min(60, fps));
                    document.getElementById('particles').textContent = '1,000';
                    document.getElementById('vertices').textContent = '2,500';
                    this.lastStatsTime = Date.now();
                }
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Test WebGL support prima di inizializzare
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            if (!gl) {
                document.getElementById('loading').innerHTML = `
                    <div style="color: #ff6b6b; text-align: center;">
                        <h3>❌ WebGL Non Supportato</h3>
                        <p>Il tuo browser non supporta WebGL</p>
                        <p>Prova ad aggiornare il browser o abilitare l'accelerazione hardware</p>
                    </div>`;
                return;
            }

            // Controlla estensioni WebGL
            const extensions = gl.getSupportedExtensions();
            console.log('WebGL Extensions:', extensions);

            try {
                console.log('Avvio applicazione...');
                new UltraCompatibleApp();
            } catch (error) {
                console.error('ERRORE CRITICO:', error);
                document.getElementById('loading').innerHTML = `
                    <div style="color: #ff6b6b; text-align: center; padding: 20px;">
                        <h3>⚠️ Errore Inizializzazione</h3>
                        <p><strong>Errore:</strong> ${error.message}</p>
                        <p><strong>Tipo:</strong> ${error.name}</p>
                        <hr style="margin: 15px 0;">
                        <h4>🔧 Soluzioni:</h4>
                        <p>1. Aggiorna il browser all'ultima versione</p>
                        <p>2. Abilita l'accelerazione hardware</p>
                        <p>3. Chiudi altre schede/programmi</p>
                        <p>4. Riavvia il browser</p>
                        <hr style="margin: 15px 0;">
                        <p><small>GPU: AMD Vega 10 - Modalità compatibilità</small></p>
                    </div>`;
            }
        });
    </script>
</body>
</html>
