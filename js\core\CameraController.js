// Controller avanzato per camera cinematografica
class CameraController {
    constructor(camera, renderer, scene) {
        this.camera = camera;
        this.renderer = renderer;
        this.scene = scene;
        this.time = 0;
        
        // Posizioni predefinite
        this.presetPositions = {
            overview: { position: new THREE.Vector3(0, 2, 8), target: new THREE.Vector3(0, 0, 0) },
            closeup: { position: new THREE.Vector3(1, 1, 3), target: new THREE.Vector3(0, 0.5, 0) },
            dramatic: { position: new THREE.Vector3(-3, 4, 5), target: new THREE.Vector3(2, 0, 0) },
            divine: { position: new THREE.Vector3(4, 6, 2), target: new THREE.Vector3(0, 1, 0) },
            creation: { position: new THREE.Vector3(0, 1, 4), target: new THREE.Vector3(0, 0.5, 1) }
        };
        
        this.currentPreset = 'overview';
        this.isAnimating = false;
        this.animationDuration = 3.0;
        this.animationTime = 0;
        
        // Setup controlli
        this.setupControls();
        this.setupCinematicMode();
        
        // Imposta posizione iniziale
        this.setPreset('overview', false);
    }
    
    setupControls() {
        // OrbitControls per controllo manuale
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 2;
        this.controls.maxDistance = 20;
        this.controls.maxPolarAngle = Math.PI;
        
        // Limiti per mantenere la composizione
        this.controls.minAzimuthAngle = -Math.PI / 2;
        this.controls.maxAzimuthAngle = Math.PI / 2;
        this.controls.minPolarAngle = Math.PI / 6;
        this.controls.maxPolarAngle = Math.PI - Math.PI / 6;
        
        // Eventi
        this.controls.addEventListener('start', () => {
            this.isUserControlling = true;
        });
        
        this.controls.addEventListener('end', () => {
            this.isUserControlling = false;
        });
    }
    
    setupCinematicMode() {
        this.cinematicMode = false;
        this.cinematicPath = [];
        this.cinematicTime = 0;
        this.cinematicDuration = 30.0; // 30 secondi di animazione cinematica
        
        // Definisci percorso cinematografico
        this.createCinematicPath();
    }
    
    createCinematicPath() {
        // Keyframes per movimento cinematografico
        this.cinematicPath = [
            { time: 0.0, position: new THREE.Vector3(0, 2, 8), target: new THREE.Vector3(0, 0, 0), fov: 75 },
            { time: 0.15, position: new THREE.Vector3(-2, 3, 6), target: new THREE.Vector3(1, 0.5, 0), fov: 60 },
            { time: 0.3, position: new THREE.Vector3(4, 4, 4), target: new THREE.Vector3(-1, 1, 0), fov: 45 },
            { time: 0.45, position: new THREE.Vector3(2, 1, 2), target: new THREE.Vector3(0, 0.5, 1), fov: 35 },
            { time: 0.6, position: new THREE.Vector3(-1, 2, 5), target: new THREE.Vector3(2, 0.8, 0), fov: 50 },
            { time: 0.75, position: new THREE.Vector3(3, 5, 3), target: new THREE.Vector3(-2, 0, 0), fov: 65 },
            { time: 0.9, position: new THREE.Vector3(0, 6, 6), target: new THREE.Vector3(0, 0, 0), fov: 70 },
            { time: 1.0, position: new THREE.Vector3(0, 2, 8), target: new THREE.Vector3(0, 0, 0), fov: 75 }
        ];
    }
    
    update(deltaTime) {
        this.time += deltaTime;
        
        if (this.cinematicMode && !this.isUserControlling) {
            this.updateCinematicCamera(deltaTime);
        } else if (this.isAnimating) {
            this.updateCameraAnimation(deltaTime);
        } else if (!this.isUserControlling) {
            this.updateIdleMovement();
        }
        
        // Aggiorna controlli
        this.controls.update();
        
        // Effetti camera
        this.updateCameraEffects();
    }
    
    updateCinematicCamera(deltaTime) {
        this.cinematicTime += deltaTime;
        const normalizedTime = (this.cinematicTime % this.cinematicDuration) / this.cinematicDuration;
        
        // Trova keyframes correnti
        let currentKeyframe = null;
        let nextKeyframe = null;
        
        for (let i = 0; i < this.cinematicPath.length - 1; i++) {
            if (normalizedTime >= this.cinematicPath[i].time && normalizedTime <= this.cinematicPath[i + 1].time) {
                currentKeyframe = this.cinematicPath[i];
                nextKeyframe = this.cinematicPath[i + 1];
                break;
            }
        }
        
        if (currentKeyframe && nextKeyframe) {
            // Interpolazione smooth tra keyframes
            const segmentDuration = nextKeyframe.time - currentKeyframe.time;
            const segmentTime = (normalizedTime - currentKeyframe.time) / segmentDuration;
            const t = this.smoothstep(segmentTime);
            
            // Interpola posizione
            const position = currentKeyframe.position.clone().lerp(nextKeyframe.position, t);
            const target = currentKeyframe.target.clone().lerp(nextKeyframe.target, t);
            const fov = currentKeyframe.fov + (nextKeyframe.fov - currentKeyframe.fov) * t;
            
            // Applica alla camera
            this.camera.position.copy(position);
            this.camera.lookAt(target);
            this.camera.fov = fov;
            this.camera.updateProjectionMatrix();
            
            // Aggiorna controlli
            this.controls.target.copy(target);
        }
    }
    
    updateCameraAnimation(deltaTime) {
        this.animationTime += deltaTime;
        const progress = Math.min(this.animationTime / this.animationDuration, 1.0);
        const t = this.easeInOutCubic(progress);
        
        if (this.animationStart && this.animationEnd) {
            // Interpola posizione e target
            const position = this.animationStart.position.clone().lerp(this.animationEnd.position, t);
            const target = this.animationStart.target.clone().lerp(this.animationEnd.target, t);
            
            this.camera.position.copy(position);
            this.controls.target.copy(target);
            
            if (progress >= 1.0) {
                this.isAnimating = false;
                this.animationTime = 0;
            }
        }
    }
    
    updateIdleMovement() {
        // Movimento sottile quando non controllata dall'utente
        if (!this.isUserControlling && !this.cinematicMode) {
            const idleAmplitude = 0.1;
            const idleSpeed = 0.3;
            
            const offsetX = Math.sin(this.time * idleSpeed) * idleAmplitude;
            const offsetY = Math.cos(this.time * idleSpeed * 0.7) * idleAmplitude * 0.5;
            const offsetZ = Math.sin(this.time * idleSpeed * 0.5) * idleAmplitude * 0.3;
            
            const basePosition = this.presetPositions[this.currentPreset].position;
            const newPosition = basePosition.clone().add(new THREE.Vector3(offsetX, offsetY, offsetZ));
            
            this.camera.position.lerp(newPosition, 0.02);
        }
    }
    
    updateCameraEffects() {
        // Shake effect per momenti drammatici
        if (this.shakeIntensity > 0) {
            const shake = new THREE.Vector3(
                (Math.random() - 0.5) * this.shakeIntensity,
                (Math.random() - 0.5) * this.shakeIntensity,
                (Math.random() - 0.5) * this.shakeIntensity
            );
            
            this.camera.position.add(shake);
            this.shakeIntensity *= 0.95; // Decay
            
            if (this.shakeIntensity < 0.001) {
                this.shakeIntensity = 0;
            }
        }
        
        // Breathing effect per FOV
        if (this.breathingEffect) {
            const breathe = Math.sin(this.time * 2.0) * 2.0;
            this.camera.fov = this.baseFOV + breathe;
            this.camera.updateProjectionMatrix();
        }
    }
    
    // Metodi pubblici
    setPreset(presetName, animate = true) {
        if (!this.presetPositions[presetName]) return;
        
        const preset = this.presetPositions[presetName];
        this.currentPreset = presetName;
        
        if (animate && !this.cinematicMode) {
            this.animateToPosition(preset.position, preset.target);
        } else {
            this.camera.position.copy(preset.position);
            this.controls.target.copy(preset.target);
            this.camera.lookAt(preset.target);
        }
    }
    
    animateToPosition(position, target) {
        this.animationStart = {
            position: this.camera.position.clone(),
            target: this.controls.target.clone()
        };
        this.animationEnd = {
            position: position.clone(),
            target: target.clone()
        };
        
        this.isAnimating = true;
        this.animationTime = 0;
    }
    
    toggleCinematicMode() {
        this.cinematicMode = !this.cinematicMode;
        if (this.cinematicMode) {
            this.cinematicTime = 0;
            this.controls.enabled = false;
        } else {
            this.controls.enabled = true;
        }
        return this.cinematicMode;
    }
    
    addShake(intensity = 0.1) {
        this.shakeIntensity = intensity;
    }
    
    setBreathingEffect(enabled) {
        this.breathingEffect = enabled;
        if (enabled) {
            this.baseFOV = this.camera.fov;
        }
    }
    
    // Funzioni di easing
    smoothstep(t) {
        return t * t * (3.0 - 2.0 * t);
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }
    
    // Controlli manuali
    reset() {
        this.setPreset('overview', true);
        this.cinematicMode = false;
        this.controls.enabled = true;
        this.shakeIntensity = 0;
        this.breathingEffect = false;
    }
    
    dispose() {
        this.controls.dispose();
    }
}
