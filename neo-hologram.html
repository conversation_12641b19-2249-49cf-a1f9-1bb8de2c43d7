<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEO - Ologramma Matrix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            overflow: hidden;
            font-family: 'Courier New', monospace;
            cursor: none;
        }
        
        #hologram-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        #neo-canvas {
            border: 2px solid #00ff41;
            border-radius: 10px;
            box-shadow: 
                0 0 20px #00ff41,
                0 0 40px #00ff41,
                0 0 80px #00ff41;
            animation: hologram-flicker 3s infinite;
        }
        
        @keyframes hologram-flicker {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
            75% { opacity: 0.9; }
        }
        
        .matrix-rain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #00ff41;
            font-size: 14px;
            text-shadow: 0 0 10px #00ff41;
        }
        
        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }
        
        .control-btn {
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid #00ff41;
            color: #00ff41;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }
        
        .control-btn:hover {
            background: rgba(0, 255, 65, 0.3);
            box-shadow: 0 0 15px #00ff41;
        }
        
        #custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border: 2px solid #00ff41;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            mix-blend-mode: difference;
            animation: cursor-pulse 1s infinite;
        }
        
        @keyframes cursor-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <div id="hologram-container">
        <canvas id="neo-canvas" width="800" height="600"></canvas>
        <canvas class="matrix-rain" id="matrix-bg" width="1920" height="1080"></canvas>
    </div>
    
    <div class="info-panel">
        <div>🤖 NEO HOLOGRAM v2.1</div>
        <div>STATUS: ONLINE</div>
        <div>MATRIX: CONNECTED</div>
        <div>FPS: <span id="fps">60</span></div>
    </div>
    
    <div class="controls">
        <button class="control-btn" onclick="toggleGlitch()">⚡ GLITCH</button>
        <button class="control-btn" onclick="toggleCode()">💚 MATRIX CODE</button>
        <button class="control-btn" onclick="changeExpression()">😎 EXPRESSION</button>
        <button class="control-btn" onclick="saveFrame()">📸 CAPTURE</button>
    </div>
    
    <div id="custom-cursor"></div>
    
    <script>
        class NeoHologram {
            constructor() {
                this.canvas = document.getElementById('neo-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.matrixCanvas = document.getElementById('matrix-bg');
                this.matrixCtx = this.matrixCanvas.getContext('2d');
                
                this.time = 0;
                this.glitchEnabled = true;
                this.matrixCodeEnabled = true;
                this.currentExpression = 0;
                this.expressions = ['neutral', 'slight_smile', 'focused', 'concerned'];
                
                // Parametri del volto
                this.faceCenter = { x: 400, y: 300 };
                this.faceWidth = 200;
                this.faceHeight = 280;
                
                // Animazione occhi
                this.eyeBlinkTimer = 0;
                this.eyeBlinkDuration = 0.2;
                this.eyeBlinkInterval = 3;
                this.eyeLookDirection = { x: 0, y: 0 };
                
                // Matrix rain
                this.matrixColumns = [];
                this.initMatrixRain();
                
                this.setupEventListeners();
                this.animate();
            }
            
            initMatrixRain() {
                const columns = Math.floor(this.matrixCanvas.width / 20);
                for (let i = 0; i < columns; i++) {
                    this.matrixColumns[i] = {
                        y: Math.random() * this.matrixCanvas.height,
                        speed: Math.random() * 3 + 1
                    };
                }
            }
            
            setupEventListeners() {
                // Custom cursor
                const cursor = document.getElementById('custom-cursor');
                document.addEventListener('mousemove', (e) => {
                    cursor.style.left = e.clientX - 10 + 'px';
                    cursor.style.top = e.clientY - 10 + 'px';
                    
                    // Occhi seguono il mouse
                    const rect = this.canvas.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;
                    
                    this.eyeLookDirection.x = (mouseX - this.faceCenter.x) * 0.1;
                    this.eyeLookDirection.y = (mouseY - this.faceCenter.y) * 0.1;
                });
                
                // Resize
                window.addEventListener('resize', () => {
                    this.matrixCanvas.width = window.innerWidth;
                    this.matrixCanvas.height = window.innerHeight;
                    this.initMatrixRain();
                });
            }
            
            drawMatrixRain() {
                if (!this.matrixCodeEnabled) return;
                
                this.matrixCtx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                this.matrixCtx.fillRect(0, 0, this.matrixCanvas.width, this.matrixCanvas.height);
                
                this.matrixCtx.fillStyle = '#00ff41';
                this.matrixCtx.font = '15px Courier New';
                
                const matrixChars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                
                for (let i = 0; i < this.matrixColumns.length; i++) {
                    const char = matrixChars[Math.floor(Math.random() * matrixChars.length)];
                    const x = i * 20;
                    const y = this.matrixColumns[i].y;
                    
                    this.matrixCtx.fillText(char, x, y);
                    
                    this.matrixColumns[i].y += this.matrixColumns[i].speed;
                    
                    if (y > this.matrixCanvas.height && Math.random() > 0.975) {
                        this.matrixColumns[i].y = 0;
                    }
                }
            }
            
            drawNeoFace() {
                const ctx = this.ctx;
                const center = this.faceCenter;
                
                // Clear canvas con effetto olografico
                ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // Effetto glitch
                if (this.glitchEnabled && Math.random() < 0.05) {
                    ctx.save();
                    ctx.translate(Math.random() * 10 - 5, Math.random() * 10 - 5);
                }
                
                // Contorno del viso (forma ovale realistica)
                this.drawFaceOutline(ctx, center);
                
                // Capelli
                this.drawHair(ctx, center);
                
                // Occhi
                this.drawEyes(ctx, center);
                
                // Naso
                this.drawNose(ctx, center);
                
                // Bocca
                this.drawMouth(ctx, center);
                
                // Dettagli facciali
                this.drawFacialDetails(ctx, center);
                
                // Effetti olografici
                this.drawHolographicEffects(ctx);
                
                if (this.glitchEnabled && Math.random() < 0.05) {
                    ctx.restore();
                }
            }
            
            drawFaceOutline(ctx, center) {
                ctx.strokeStyle = `rgba(0, 255, 65, ${0.8 + Math.sin(this.time * 2) * 0.2})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                // Contorno realistico del viso di Neo
                ctx.ellipse(center.x, center.y, this.faceWidth/2, this.faceHeight/2, 0, 0, Math.PI * 2);
                ctx.stroke();
                
                // Linea della mascella
                ctx.beginPath();
                ctx.moveTo(center.x - this.faceWidth/3, center.y + this.faceHeight/3);
                ctx.quadraticCurveTo(center.x, center.y + this.faceHeight/2.2, center.x + this.faceWidth/3, center.y + this.faceHeight/3);
                ctx.stroke();
            }
            
            drawHair(ctx, center) {
                ctx.strokeStyle = `rgba(0, 255, 65, 0.6)`;
                ctx.lineWidth = 1;
                
                // Capelli caratteristici di Neo
                for (let i = 0; i < 20; i++) {
                    const angle = (i / 20) * Math.PI - Math.PI/2;
                    const startX = center.x + Math.cos(angle) * (this.faceWidth/2 - 10);
                    const startY = center.y + Math.sin(angle) * (this.faceHeight/2 - 10);
                    const endX = startX + Math.cos(angle) * 30;
                    const endY = startY + Math.sin(angle) * 30;
                    
                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX + Math.sin(this.time + i) * 5, endY);
                    ctx.stroke();
                }
            }
            
            drawEyes(ctx, center) {
                const eyeY = center.y - 30;
                const eyeWidth = 25;
                const eyeHeight = 15;
                const eyeSpacing = 60;
                
                // Calcola blink
                this.eyeBlinkTimer += 0.016;
                let blinkFactor = 1;
                if (this.eyeBlinkTimer > this.eyeBlinkInterval) {
                    const blinkProgress = (this.eyeBlinkTimer - this.eyeBlinkInterval) / this.eyeBlinkDuration;
                    if (blinkProgress < 1) {
                        blinkFactor = Math.abs(Math.sin(blinkProgress * Math.PI));
                    } else {
                        this.eyeBlinkTimer = 0;
                    }
                }
                
                // Occhio sinistro
                this.drawEye(ctx, center.x - eyeSpacing/2, eyeY, eyeWidth, eyeHeight * blinkFactor, 'left');
                
                // Occhio destro
                this.drawEye(ctx, center.x + eyeSpacing/2, eyeY, eyeWidth, eyeHeight * blinkFactor, 'right');
            }
            
            drawEye(ctx, x, y, width, height, side) {
                // Contorno occhio
                ctx.strokeStyle = `rgba(0, 255, 65, 0.8)`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.ellipse(x, y, width, height, 0, 0, Math.PI * 2);
                ctx.stroke();
                
                if (height > 5) { // Solo se l'occhio è aperto
                    // Pupilla che segue il mouse
                    const pupilX = x + this.eyeLookDirection.x;
                    const pupilY = y + this.eyeLookDirection.y;
                    
                    // Iride
                    ctx.fillStyle = `rgba(0, 255, 65, 0.3)`;
                    ctx.beginPath();
                    ctx.arc(pupilX, pupilY, 8, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Pupilla
                    ctx.fillStyle = `rgba(0, 255, 65, 0.9)`;
                    ctx.beginPath();
                    ctx.arc(pupilX, pupilY, 4, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Riflesso
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.beginPath();
                    ctx.arc(pupilX - 2, pupilY - 2, 1, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                // Sopracciglia
                ctx.strokeStyle = `rgba(0, 255, 65, 0.6)`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x - width, y - height - 10);
                ctx.quadraticCurveTo(x, y - height - 15, x + width, y - height - 10);
                ctx.stroke();
            }
            
            drawNose(ctx, center) {
                ctx.strokeStyle = `rgba(0, 255, 65, 0.7)`;
                ctx.lineWidth = 1.5;
                
                // Ponte del naso
                ctx.beginPath();
                ctx.moveTo(center.x, center.y - 20);
                ctx.lineTo(center.x, center.y + 10);
                ctx.stroke();
                
                // Narici
                ctx.beginPath();
                ctx.arc(center.x - 8, center.y + 8, 3, 0, Math.PI);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.arc(center.x + 8, center.y + 8, 3, 0, Math.PI);
                ctx.stroke();
            }
            
            drawMouth(ctx, center) {
                const mouthY = center.y + 50;
                const expression = this.expressions[this.currentExpression];
                
                ctx.strokeStyle = `rgba(0, 255, 65, 0.8)`;
                ctx.lineWidth = 2;
                
                ctx.beginPath();
                switch(expression) {
                    case 'neutral':
                        ctx.moveTo(center.x - 20, mouthY);
                        ctx.lineTo(center.x + 20, mouthY);
                        break;
                    case 'slight_smile':
                        ctx.moveTo(center.x - 20, mouthY);
                        ctx.quadraticCurveTo(center.x, mouthY - 5, center.x + 20, mouthY);
                        break;
                    case 'focused':
                        ctx.moveTo(center.x - 15, mouthY);
                        ctx.quadraticCurveTo(center.x, mouthY + 3, center.x + 15, mouthY);
                        break;
                    case 'concerned':
                        ctx.moveTo(center.x - 20, mouthY + 2);
                        ctx.quadraticCurveTo(center.x, mouthY + 8, center.x + 20, mouthY + 2);
                        break;
                }
                ctx.stroke();
            }
            
            drawFacialDetails(ctx, center) {
                ctx.strokeStyle = `rgba(0, 255, 65, 0.4)`;
                ctx.lineWidth = 1;
                
                // Linee del viso per definizione
                // Zigomi
                ctx.beginPath();
                ctx.moveTo(center.x - this.faceWidth/3, center.y - 10);
                ctx.lineTo(center.x - this.faceWidth/4, center.y + 20);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(center.x + this.faceWidth/3, center.y - 10);
                ctx.lineTo(center.x + this.faceWidth/4, center.y + 20);
                ctx.stroke();
                
                // Linea del mento
                ctx.beginPath();
                ctx.moveTo(center.x - 15, center.y + this.faceHeight/2.5);
                ctx.lineTo(center.x + 15, center.y + this.faceHeight/2.5);
                ctx.stroke();
            }
            
            drawHolographicEffects(ctx) {
                // Scanlines
                ctx.strokeStyle = `rgba(0, 255, 65, ${0.1 + Math.sin(this.time * 10) * 0.05})`;
                ctx.lineWidth = 1;
                for (let y = 0; y < this.canvas.height; y += 4) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(this.canvas.width, y);
                    ctx.stroke();
                }
                
                // Interferenze
                if (Math.random() < 0.1) {
                    ctx.fillStyle = `rgba(0, 255, 65, ${Math.random() * 0.3})`;
                    ctx.fillRect(
                        Math.random() * this.canvas.width,
                        Math.random() * this.canvas.height,
                        Math.random() * 100,
                        Math.random() * 20
                    );
                }
                
                // Particelle luminose
                for (let i = 0; i < 10; i++) {
                    const x = this.faceCenter.x + (Math.random() - 0.5) * this.faceWidth * 1.5;
                    const y = this.faceCenter.y + (Math.random() - 0.5) * this.faceHeight * 1.5;
                    const size = Math.random() * 3;
                    
                    ctx.fillStyle = `rgba(0, 255, 65, ${Math.random() * 0.8})`;
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            animate() {
                this.time += 0.016;
                
                this.drawMatrixRain();
                this.drawNeoFace();
                
                // Update FPS
                if (Math.floor(this.time * 60) % 60 === 0) {
                    document.getElementById('fps').textContent = '60';
                }
                
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // Controlli
        let neoHologram;
        
        function toggleGlitch() {
            neoHologram.glitchEnabled = !neoHologram.glitchEnabled;
        }
        
        function toggleCode() {
            neoHologram.matrixCodeEnabled = !neoHologram.matrixCodeEnabled;
        }
        
        function changeExpression() {
            neoHologram.currentExpression = (neoHologram.currentExpression + 1) % neoHologram.expressions.length;
        }
        
        function saveFrame() {
            const link = document.createElement('a');
            link.download = 'neo-hologram.png';
            link.href = neoHologram.canvas.toDataURL();
            link.click();
        }
        
        // Inizializza quando la pagina è caricata
        document.addEventListener('DOMContentLoaded', () => {
            neoHologram = new NeoHologram();
        });
    </script>
</body>
</html>
