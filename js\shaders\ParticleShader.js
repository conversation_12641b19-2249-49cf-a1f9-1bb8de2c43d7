// Shader per sistema particellare divino avanzato
const ParticleShader = {
    vertexShader: `
        attribute float size;
        attribute vec3 velocity;
        attribute float life;
        attribute float energy;
        attribute vec3 startPosition;
        
        uniform float time;
        uniform float deltaTime;
        uniform vec3 godFinger;
        uniform vec3 adamFinger;
        uniform float creationIntensity;
        uniform float particleScale;
        
        varying float vLife;
        varying float vEnergy;
        varying vec3 vColor;
        varying float vSize;
        
        // Noise per movimento organico
        vec3 mod289(vec3 x) {
            return x - floor(x * (1.0 / 289.0)) * 289.0;
        }
        
        vec4 mod289(vec4 x) {
            return x - floor(x * (1.0 / 289.0)) * 289.0;
        }
        
        vec4 permute(vec4 x) {
            return mod289(((x*34.0)****)*x);
        }
        
        vec4 taylorInvSqrt(vec4 r) {
            return 1.79284291400159 - 0.85373472095314 * r;
        }
        
        float snoise(vec3 v) {
            const vec2 C = vec2(1.0/6.0, 1.0/3.0);
            const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);
            
            vec3 i = floor(v + dot(v, C.yyy));
            vec3 x0 = v - i + dot(i, C.xxx);
            
            vec3 g = step(x0.yzx, x0.xyz);
            vec3 l = 1.0 - g;
            vec3 i1 = min(g.xyz, l.zxy);
            vec3 i2 = max(g.xyz, l.zxy);
            
            vec3 x1 = x0 - i1 + C.xxx;
            vec3 x2 = x0 - i2 + C.yyy;
            vec3 x3 = x0 - D.yyy;
            
            i = mod289(i);
            vec4 p = permute(permute(permute(
                i.z + vec4(0.0, i1.z, i2.z, 1.0))
                + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                + i.x + vec4(0.0, i1.x, i2.x, 1.0));
                
            float n_ = 0.142857142857;
            vec3 ns = n_ * D.wyz - D.xzx;
            
            vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
            
            vec4 x_ = floor(j * ns.z);
            vec4 y_ = floor(j - 7.0 * x_);
            
            vec4 x = x_ *ns.x + ns.yyyy;
            vec4 y = y_ *ns.x + ns.yyyy;
            vec4 h = 1.0 - abs(x) - abs(y);
            
            vec4 b0 = vec4(x.xy, y.xy);
            vec4 b1 = vec4(x.zw, y.zw);
            
            vec4 s0 = floor(b0)*2.0 + 1.0;
            vec4 s1 = floor(b1)*2.0 + 1.0;
            vec4 sh = -step(h, vec4(0.0));
            
            vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
            vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;
            
            vec3 p0 = vec3(a0.xy, h.x);
            vec3 p1 = vec3(a0.zw, h.y);
            vec3 p2 = vec3(a1.xy, h.z);
            vec3 p3 = vec3(a1.zw, h.w);
            
            vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
            p0 *= norm.x;
            p1 *= norm.y;
            p2 *= norm.z;
            p3 *= norm.w;
            
            vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
            m = m * m;
            return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
        }
        
        void main() {
            vLife = life;
            vEnergy = energy;
            
            // Calcolo posizione particella con fisica avanzata
            vec3 pos = position;
            
            // Forza di attrazione tra le dita
            vec3 toGod = godFinger - pos;
            vec3 toAdam = adamFinger - pos;
            float distToGod = length(toGod);
            float distToAdam = length(toAdam);
            
            // Campo di forza divino
            vec3 divineForce = vec3(0.0);
            if (distToGod > 0.0) {
                divineForce += normalize(toGod) * (creationIntensity / (distToGod * distToGod + 0.1));
            }
            if (distToAdam > 0.0) {
                divineForce += normalize(toAdam) * (creationIntensity * 0.3 / (distToAdam * distToAdam + 0.1));
            }
            
            // Movimento turbulento con noise
            vec3 noisePos = pos * 0.1 + time * 0.2;
            vec3 turbulence = vec3(
                snoise(noisePos),
                snoise(noisePos + vec3(100.0)),
                snoise(noisePos + vec3(200.0))
            ) * 0.5;
            
            // Spirale divina
            float spiral = time * 2.0 + length(pos) * 0.1;
            vec3 spiralForce = vec3(
                cos(spiral) * sin(spiral * 0.7),
                sin(spiral) * cos(spiral * 0.3),
                cos(spiral * 1.3)
            ) * energy * 0.3;
            
            // Combinazione delle forze
            vec3 totalForce = divineForce + turbulence + spiralForce;
            pos += totalForce * deltaTime;
            
            // Effetto di vita della particella
            float lifeFactor = smoothstep(0.0, 0.1, life) * smoothstep(1.0, 0.8, life);
            
            // Colore basato su energia e posizione
            float energyNorm = clamp(energy, 0.0, 1.0);
            vColor = mix(
                vec3(1.0, 0.8, 0.4),  // Oro divino
                vec3(0.4, 0.8, 1.0),  // Blu celestiale
                energyNorm
            );
            
            // Intensità basata sulla vicinanza alle dita
            float proximity = 1.0 / (min(distToGod, distToAdam) + 0.1);
            vColor *= proximity * creationIntensity;
            
            // Dimensione particella
            vSize = size * particleScale * lifeFactor * (1.0 + energy * 2.0);
            
            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
            gl_Position = projectionMatrix * mvPosition;
            gl_PointSize = vSize * (300.0 / -mvPosition.z);
        }
    `,
    
    fragmentShader: `
        uniform sampler2D particleTexture;
        uniform float time;
        uniform float opacity;
        
        varying float vLife;
        varying float vEnergy;
        varying vec3 vColor;
        varying float vSize;
        
        void main() {
            // Texture circolare per particelle
            vec2 center = gl_PointCoord - vec2(0.5);
            float dist = length(center);
            
            if (dist > 0.5) discard;
            
            // Gradiente radiale
            float alpha = 1.0 - smoothstep(0.0, 0.5, dist);
            alpha *= alpha; // Quadratico per effetto più morbido
            
            // Pulsazione basata su energia
            float pulse = sin(time * 10.0 + vEnergy * 20.0) * 0.3 + 0.7;
            alpha *= pulse;
            
            // Effetto scintillio
            float sparkle = sin(time * 30.0 + gl_FragCoord.x * 0.1 + gl_FragCoord.y * 0.1);
            sparkle = smoothstep(0.8, 1.0, sparkle);
            
            vec3 finalColor = vColor * (1.0 + sparkle * 0.5);
            
            // Vita della particella
            float lifeFactor = smoothstep(0.0, 0.1, vLife) * smoothstep(1.0, 0.7, vLife);
            alpha *= lifeFactor * opacity;
            
            // HDR glow
            finalColor *= 1.0 + vEnergy * 2.0;
            
            gl_FragColor = vec4(finalColor, alpha);
        }
    `
};
