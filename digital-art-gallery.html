<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galleria Arte Digitale - Opere Vendibili</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            overflow-x: hidden;
        }
        
        .header {
            text-align: center;
            padding: 40px 20px;
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffd700, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            padding: 40px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .artwork {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .artwork:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .artwork-canvas {
            width: 100%;
            height: 300px;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 2px solid rgba(255,255,255,0.1);
        }
        
        .artwork-info {
            text-align: center;
        }
        
        .artwork-title {
            font-size: 1.5em;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .artwork-description {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .artwork-price {
            font-size: 1.3em;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        
        .buy-button {
            background: linear-gradient(45deg, #ff6b35, #ffd700);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
        }
        
        .buy-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255,107,53,0.4);
        }
        
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        
        .control-btn {
            background: rgba(0,0,0,0.7);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 500px;
            backdrop-filter: blur(20px);
        }
        
        .close-modal {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 2em;
            cursor: pointer;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Galleria Arte Digitale</h1>
        <p>Opere d'arte generative uniche - Pronte per la vendita</p>
    </div>
    
    <div class="controls">
        <button class="control-btn" onclick="regenerateAll()">🔄 Rigenera Tutto</button>
        <button class="control-btn" onclick="toggleAnimation()">⏸️ Pausa</button>
        <button class="control-btn" onclick="exportAll()">💾 Esporta Tutto</button>
    </div>
    
    <div class="gallery" id="gallery">
        <!-- Le opere verranno generate dinamicamente -->
    </div>
    
    <div class="modal" id="purchaseModal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal()">&times;</span>
            <h2>🎉 Acquisto Opera d'Arte</h2>
            <p id="modalArtworkTitle"></p>
            <p id="modalArtworkPrice"></p>
            <br>
            <p><strong>Formati disponibili:</strong></p>
            <p>• PNG ad alta risoluzione (4K)</p>
            <p>• SVG vettoriale (infinitamente scalabile)</p>
            <p>• Video MP4 animato (10 secondi loop)</p>
            <p>• NFT su blockchain (certificato di autenticità)</p>
            <br>
            <button class="buy-button" onclick="completePurchase()">💳 Acquista Ora</button>
        </div>
    </div>
    
    <script>
        // Configurazione opere d'arte
        const artworks = [
            {
                title: "Cosmic Mandala",
                description: "Mandala generativo con pattern cosmici e geometrie sacre. Ogni rendering è unico.",
                price: "€299",
                type: "mandala"
            },
            {
                title: "Digital Waves",
                description: "Onde digitali fluide con effetti di interferenza. Movimento ipnotico perpetuo.",
                price: "€199",
                type: "waves"
            },
            {
                title: "Fractal Garden",
                description: "Giardino frattale con crescita organica. Natura matematica in evoluzione.",
                price: "€399",
                type: "fractal"
            },
            {
                title: "Neon Dreams",
                description: "Paesaggio cyberpunk con luci al neon. Estetica retrofuturista animata.",
                price: "€249",
                type: "neon"
            },
            {
                title: "Particle Symphony",
                description: "Sinfonia di particelle che danzano seguendo algoritmi musicali.",
                price: "€179",
                type: "particles"
            },
            {
                title: "Abstract Emotions",
                description: "Rappresentazione astratta delle emozioni umane attraverso colori e forme.",
                price: "€329",
                type: "abstract"
            }
        ];
        
        let animationRunning = true;
        let canvases = [];
        
        // Inizializza la galleria
        function initGallery() {
            const gallery = document.getElementById('gallery');
            
            artworks.forEach((artwork, index) => {
                const artworkDiv = document.createElement('div');
                artworkDiv.className = 'artwork';
                artworkDiv.innerHTML = `
                    <canvas class="artwork-canvas" id="canvas-${index}"></canvas>
                    <div class="artwork-info">
                        <h3 class="artwork-title">${artwork.title}</h3>
                        <p class="artwork-description">${artwork.description}</p>
                        <p class="artwork-price">${artwork.price}</p>
                        <button class="buy-button" onclick="openPurchaseModal('${artwork.title}', '${artwork.price}')">
                            🛒 Acquista Opera
                        </button>
                    </div>
                `;
                gallery.appendChild(artworkDiv);
                
                // Inizializza canvas
                const canvas = document.getElementById(`canvas-${index}`);
                canvas.width = 400;
                canvas.height = 300;
                canvases.push({
                    canvas: canvas,
                    ctx: canvas.getContext('2d'),
                    type: artwork.type,
                    time: 0
                });
            });
            
            // Avvia animazioni
            animate();
        }
        
        // Funzioni di rendering per ogni tipo di opera
        function renderMandala(ctx, time, width, height) {
            ctx.fillStyle = '#000011';
            ctx.fillRect(0, 0, width, height);
            
            const centerX = width / 2;
            const centerY = height / 2;
            const layers = 8;
            
            for (let layer = 0; layer < layers; layer++) {
                const radius = (layer + 1) * 30;
                const points = (layer + 1) * 6;
                
                ctx.strokeStyle = `hsl(${(time * 20 + layer * 45) % 360}, 70%, 60%)`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let i = 0; i <= points; i++) {
                    const angle = (i / points) * Math.PI * 2 + time * 0.5;
                    const r = radius + Math.sin(time * 2 + layer) * 10;
                    const x = centerX + Math.cos(angle) * r;
                    const y = centerY + Math.sin(angle) * r;
                    
                    if (i === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.closePath();
                ctx.stroke();
                
                // Punti luminosi
                for (let i = 0; i < points; i++) {
                    const angle = (i / points) * Math.PI * 2 + time * 0.5;
                    const r = radius + Math.sin(time * 2 + layer) * 10;
                    const x = centerX + Math.cos(angle) * r;
                    const y = centerY + Math.sin(angle) * r;
                    
                    ctx.fillStyle = `hsl(${(time * 30 + i * 60) % 360}, 80%, 70%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 3, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }
        
        function renderWaves(ctx, time, width, height) {
            const gradient = ctx.createLinearGradient(0, 0, 0, height);
            gradient.addColorStop(0, '#001122');
            gradient.addColorStop(1, '#000033');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            const waves = 5;
            for (let w = 0; w < waves; w++) {
                ctx.strokeStyle = `rgba(${100 + w * 30}, ${150 + w * 20}, 255, 0.7)`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                
                for (let x = 0; x <= width; x += 2) {
                    const y = height/2 + 
                             Math.sin((x * 0.01) + (time * 2) + (w * 0.5)) * 30 +
                             Math.sin((x * 0.005) + (time * 1.5) + (w * 0.3)) * 20;
                    
                    if (x === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();
            }
        }
        
        function renderFractal(ctx, time, width, height) {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, width, height);
            
            function drawBranch(x, y, length, angle, depth) {
                if (depth === 0 || length < 2) return;
                
                const endX = x + Math.cos(angle) * length;
                const endY = y + Math.sin(angle) * length;
                
                ctx.strokeStyle = `hsl(${120 + depth * 20 + time * 10}, 70%, ${50 + depth * 5}%)`;
                ctx.lineWidth = depth;
                ctx.beginPath();
                ctx.moveTo(x, y);
                ctx.lineTo(endX, endY);
                ctx.stroke();
                
                const newLength = length * 0.7;
                const angleOffset = Math.sin(time + depth) * 0.3;
                
                drawBranch(endX, endY, newLength, angle - 0.5 + angleOffset, depth - 1);
                drawBranch(endX, endY, newLength, angle + 0.5 + angleOffset, depth - 1);
            }
            
            drawBranch(width/2, height - 20, 80, -Math.PI/2, 8);
        }
        
        function renderNeon(ctx, time, width, height) {
            ctx.fillStyle = '#0a0a0a';
            ctx.fillRect(0, 0, width, height);
            
            // Griglia neon
            ctx.strokeStyle = `rgba(0, 255, 255, ${0.3 + Math.sin(time * 3) * 0.2})`;
            ctx.lineWidth = 1;
            
            for (let x = 0; x < width; x += 20) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }
            
            for (let y = 0; y < height; y += 20) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
            
            // Forme neon fluttuanti
            for (let i = 0; i < 5; i++) {
                const x = (width/6) * (i + 1) + Math.sin(time + i) * 30;
                const y = height/2 + Math.cos(time * 1.5 + i) * 50;
                const size = 20 + Math.sin(time * 2 + i) * 10;
                
                ctx.shadowColor = `hsl(${(time * 50 + i * 72) % 360}, 100%, 50%)`;
                ctx.shadowBlur = 20;
                ctx.fillStyle = `hsl(${(time * 50 + i * 72) % 360}, 100%, 50%)`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
        }
        
        function renderParticles(ctx, time, width, height) {
            ctx.fillStyle = 'rgba(0, 0, 20, 0.1)';
            ctx.fillRect(0, 0, width, height);
            
            const particleCount = 50;
            for (let i = 0; i < particleCount; i++) {
                const x = (width/2) + Math.sin(time * 0.5 + i * 0.1) * (width/3) + Math.cos(time + i) * 50;
                const y = (height/2) + Math.cos(time * 0.7 + i * 0.15) * (height/3) + Math.sin(time * 1.2 + i) * 30;
                const size = 2 + Math.sin(time * 3 + i) * 2;
                
                ctx.fillStyle = `hsl(${(time * 30 + i * 7) % 360}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
                
                // Connessioni tra particelle vicine
                for (let j = i + 1; j < Math.min(i + 5, particleCount); j++) {
                    const x2 = (width/2) + Math.sin(time * 0.5 + j * 0.1) * (width/3) + Math.cos(time + j) * 50;
                    const y2 = (height/2) + Math.cos(time * 0.7 + j * 0.15) * (height/3) + Math.sin(time * 1.2 + j) * 30;
                    const distance = Math.sqrt((x2-x)**2 + (y2-y)**2);
                    
                    if (distance < 80) {
                        ctx.strokeStyle = `rgba(255, 255, 255, ${0.3 * (1 - distance/80)})`;
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(x, y);
                        ctx.lineTo(x2, y2);
                        ctx.stroke();
                    }
                }
            }
        }
        
        function renderAbstract(ctx, time, width, height) {
            const gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, width/2);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(1, '#16213e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Forme astratte emotive
            const emotions = [
                { color: [255, 100, 100], intensity: Math.sin(time) },      // Rabbia
                { color: [100, 100, 255], intensity: Math.cos(time * 1.3) }, // Tristezza
                { color: [255, 255, 100], intensity: Math.sin(time * 0.7) }, // Gioia
                { color: [100, 255, 100], intensity: Math.cos(time * 1.7) }  // Speranza
            ];
            
            emotions.forEach((emotion, i) => {
                const intensity = (emotion.intensity + 1) / 2; // 0-1
                const alpha = intensity * 0.7;
                const size = 50 + intensity * 100;
                
                const x = (width / 5) * (i + 1) + Math.sin(time + i) * 50;
                const y = height/2 + Math.cos(time * 1.2 + i) * 80;
                
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, size);
                gradient.addColorStop(0, `rgba(${emotion.color.join(',')}, ${alpha})`);
                gradient.addColorStop(1, `rgba(${emotion.color.join(',')}, 0)`);
                
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            });
        }
        
        // Loop di animazione
        function animate() {
            if (!animationRunning) {
                requestAnimationFrame(animate);
                return;
            }
            
            canvases.forEach(canvasData => {
                canvasData.time += 0.02;
                const { ctx, type, canvas, time } = canvasData;
                const { width, height } = canvas;
                
                switch(type) {
                    case 'mandala':
                        renderMandala(ctx, time, width, height);
                        break;
                    case 'waves':
                        renderWaves(ctx, time, width, height);
                        break;
                    case 'fractal':
                        renderFractal(ctx, time, width, height);
                        break;
                    case 'neon':
                        renderNeon(ctx, time, width, height);
                        break;
                    case 'particles':
                        renderParticles(ctx, time, width, height);
                        break;
                    case 'abstract':
                        renderAbstract(ctx, time, width, height);
                        break;
                }
            });
            
            requestAnimationFrame(animate);
        }
        
        // Funzioni di controllo
        function regenerateAll() {
            canvases.forEach(canvasData => {
                canvasData.time = Math.random() * 100;
            });
        }
        
        function toggleAnimation() {
            animationRunning = !animationRunning;
            const btn = document.querySelector('.control-btn:nth-child(2)');
            btn.textContent = animationRunning ? '⏸️ Pausa' : '▶️ Play';
        }
        
        function exportAll() {
            canvases.forEach((canvasData, index) => {
                const link = document.createElement('a');
                link.download = `artwork-${artworks[index].title.replace(/\s+/g, '-').toLowerCase()}.png`;
                link.href = canvasData.canvas.toDataURL();
                link.click();
            });
            alert('🎨 Tutte le opere sono state esportate!');
        }
        
        // Modal di acquisto
        function openPurchaseModal(title, price) {
            document.getElementById('modalArtworkTitle').textContent = title;
            document.getElementById('modalArtworkPrice').textContent = price;
            document.getElementById('purchaseModal').style.display = 'flex';
        }
        
        function closeModal() {
            document.getElementById('purchaseModal').style.display = 'none';
        }
        
        function completePurchase() {
            alert('🎉 Grazie per l\'acquisto! Riceverai i file ad alta risoluzione via email entro 24 ore.');
            closeModal();
        }
        
        // Inizializza quando la pagina è caricata
        document.addEventListener('DOMContentLoaded', initGallery);
    </script>
</body>
</html>
