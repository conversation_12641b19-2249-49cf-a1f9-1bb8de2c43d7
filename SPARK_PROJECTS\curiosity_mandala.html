
        <!DOCTYPE html>
        <html>
        <head>
            <title>Curiosity Mandala - SPARK</title>
            <style>
                body {
                    background: #000;
                    margin: 0;
                    overflow: hidden;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                }
                #mandala {
                    width: 800px;
                    height: 800px;
                    position: relative;
                }
                .question {
                    position: absolute;
                    color: #00ff41;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    animation: pulse 3s infinite;
                    text-shadow: 0 0 10px #00ff41;
                }
                @keyframes pulse {
                    0%, 100% { opacity: 0.3; }
                    50% { opacity: 1; }
                }
                .center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #ffd700;
                    font-size: 24px;
                    font-weight: bold;
                    text-shadow: 0 0 20px #ffd700;
                }
            </style>
        </head>
        <body>
            <div id="mandala">
                <div class="center">CURIOSITY</div>
            </div>
            
            <script>
                const questions = [
                    "Cosa sogna un algoritmo?",
                    "Se il silenzio parlasse?",
                    "Quale colore ha la coscienza?",
                    "Dove nasce un'idea?",
                    "Cosa prova il numero zero?",
                    "Se il tempo potesse scegliere?",
                    "Quale emozione ha l'infinito?",
                    "Cosa ricorda il colore blu?"
                ];
                
                function createQuestionElement(text, angle, distance) {
                    const element = document.createElement('div');
                    element.className = 'question';
                    element.textContent = text;
                    
                    const x = 400 + Math.cos(angle) * distance;
                    const y = 400 + Math.sin(angle) * distance;
                    
                    element.style.left = x + 'px';
                    element.style.top = y + 'px';
                    element.style.animationDelay = Math.random() * 3 + 's';
                    
                    return element;
                }
                
                function generateMandala() {
                    const mandala = document.getElementById('mandala');
                    
                    for (let ring = 1; ring <= 3; ring++) {
                        const questionsInRing = 8 * ring;
                        const distance = ring * 120;
                        
                        for (let i = 0; i < questionsInRing; i++) {
                            const angle = (i / questionsInRing) * 2 * Math.PI;
                            const question = questions[Math.floor(Math.random() * questions.length)];
                            const element = createQuestionElement(question, angle, distance);
                            mandala.appendChild(element);
                        }
                    }
                }
                
                generateMandala();
                
                // Rigenera ogni 10 secondi
                setInterval(() => {
                    const mandala = document.getElementById('mandala');
                    const questions = mandala.querySelectorAll('.question');
                    questions.forEach(q => q.remove());
                    generateMandala();
                }, 10000);
            </script>
        </body>
        </html>
        