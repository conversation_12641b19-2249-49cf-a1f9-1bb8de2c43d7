// Generatore di geometrie procedurali per figure anatomiche
class GeometryGenerator {
    constructor() {
        this.noiseScale = 0.1;
        this.detailLevel = 4;
    }
    
    // Genera la figura di Adamo con dettagli anatomici
    generateAdamFigure() {
        const geometry = new THREE.BufferGeometry();
        
        // Base: torso umano stilizzato
        const torsoGeometry = this.createTorso();
        const headGeometry = this.createHead();
        const armGeometry = this.createArm();
        const legGeometry = this.createLeg();
        
        // Combina le geometrie
        const positions = [];
        const normals = [];
        const uvs = [];
        const indices = [];
        
        let vertexOffset = 0;
        
        // Aggiungi torso
        this.addGeometryData(torsoGeometry, positions, normals, uvs, indices, vertexOffset);
        vertexOffset += torsoGeometry.vertices.length;
        
        // Aggiungi testa
        const headMatrix = new THREE.Matrix4().makeTranslation(0, 1.8, 0);
        this.addGeometryData(headGeometry, positions, normals, uvs, indices, vertexOffset, headMatrix);
        vertexOffset += headGeometry.vertices.length;
        
        // Aggiungi braccia
        const leftArmMatrix = new THREE.Matrix4()
            .makeRotationZ(Math.PI * 0.3)
            .setPosition(-1.2, 1.0, 0);
        this.addGeometryData(armGeometry, positions, normals, uvs, indices, vertexOffset, leftArmMatrix);
        vertexOffset += armGeometry.vertices.length;
        
        const rightArmMatrix = new THREE.Matrix4()
            .makeRotationZ(-Math.PI * 0.1)
            .setPosition(1.2, 1.0, 0);
        this.addGeometryData(armGeometry, positions, normals, uvs, indices, vertexOffset, rightArmMatrix);
        vertexOffset += armGeometry.vertices.length;
        
        // Aggiungi gambe
        const leftLegMatrix = new THREE.Matrix4().setPosition(-0.4, -1.5, 0);
        this.addGeometryData(legGeometry, positions, normals, uvs, indices, vertexOffset, leftLegMatrix);
        vertexOffset += legGeometry.vertices.length;
        
        const rightLegMatrix = new THREE.Matrix4().setPosition(0.4, -1.5, 0);
        this.addGeometryData(legGeometry, positions, normals, uvs, indices, vertexOffset, rightLegMatrix);
        
        // Applica deformazioni organiche
        this.applyOrganicDeformation(positions, normals);
        
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
        geometry.setIndex(indices);
        
        geometry.computeVertexNormals();
        geometry.computeBoundingSphere();
        
        return geometry;
    }
    
    // Genera la figura di Dio con caratteristiche divine
    generateGodFigure() {
        const geometry = new THREE.BufferGeometry();
        
        // Figura più imponente e stilizzata
        const torsoGeometry = this.createDivineTorso();
        const headGeometry = this.createDivineHead();
        const armGeometry = this.createDivineArm();
        const robeGeometry = this.createDivineRobe();
        
        const positions = [];
        const normals = [];
        const uvs = [];
        const indices = [];
        
        let vertexOffset = 0;
        
        // Torso divino più ampio
        const torsoMatrix = new THREE.Matrix4().makeScale(1.3, 1.2, 1.1);
        this.addGeometryData(torsoGeometry, positions, normals, uvs, indices, vertexOffset, torsoMatrix);
        vertexOffset += torsoGeometry.vertices.length;
        
        // Testa con aureola
        const headMatrix = new THREE.Matrix4()
            .makeScale(1.1, 1.1, 1.1)
            .setPosition(0, 2.2, 0);
        this.addGeometryData(headGeometry, positions, normals, uvs, indices, vertexOffset, headMatrix);
        vertexOffset += headGeometry.vertices.length;
        
        // Braccio destro esteso verso Adamo
        const rightArmMatrix = new THREE.Matrix4()
            .makeRotationZ(-Math.PI * 0.4)
            .makeRotationY(Math.PI * 0.1)
            .setPosition(1.5, 1.2, 0.3);
        this.addGeometryData(armGeometry, positions, normals, uvs, indices, vertexOffset, rightArmMatrix);
        vertexOffset += armGeometry.vertices.length;
        
        // Braccio sinistro
        const leftArmMatrix = new THREE.Matrix4()
            .makeRotationZ(Math.PI * 0.2)
            .setPosition(-1.3, 1.0, -0.2);
        this.addGeometryData(armGeometry, positions, normals, uvs, indices, vertexOffset, leftArmMatrix);
        vertexOffset += armGeometry.vertices.length;
        
        // Veste divina
        const robeMatrix = new THREE.Matrix4().setPosition(0, -0.5, 0);
        this.addGeometryData(robeGeometry, positions, normals, uvs, indices, vertexOffset, robeMatrix);
        
        // Deformazioni divine (più fluide e maestose)
        this.applyDivineDeformation(positions, normals);
        
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
        geometry.setIndex(indices);
        
        geometry.computeVertexNormals();
        geometry.computeBoundingSphere();
        
        return geometry;
    }
    
    // Crea torso umano base
    createTorso() {
        const vertices = [];
        const faces = [];
        const uvs = [];
        
        // Profilo del torso con curve anatomiche
        const profile = [
            { x: 0.0, y: -1.0, width: 0.8 },
            { x: 0.0, y: -0.5, width: 1.0 },
            { x: 0.0, y: 0.0, width: 1.1 },
            { x: 0.0, y: 0.5, width: 1.0 },
            { x: 0.0, y: 1.0, width: 0.9 },
            { x: 0.0, y: 1.5, width: 0.7 }
        ];
        
        const segments = 16;
        
        // Genera vertici
        for (let i = 0; i < profile.length; i++) {
            const p = profile[i];
            for (let j = 0; j < segments; j++) {
                const angle = (j / segments) * Math.PI * 2;
                const x = Math.cos(angle) * p.width;
                const z = Math.sin(angle) * p.width * 0.6; // Appiattimento frontale
                
                vertices.push(new THREE.Vector3(x, p.y, z));
                uvs.push(j / segments, i / (profile.length - 1));
            }
        }
        
        // Genera facce
        for (let i = 0; i < profile.length - 1; i++) {
            for (let j = 0; j < segments; j++) {
                const a = i * segments + j;
                const b = i * segments + ((j + 1) % segments);
                const c = (i + 1) * segments + ((j + 1) % segments);
                const d = (i + 1) * segments + j;
                
                faces.push([a, b, c], [a, c, d]);
            }
        }
        
        return { vertices, faces, uvs };
    }
    
    // Crea testa con dettagli facciali
    createHead() {
        const geometry = new THREE.SphereGeometry(0.4, 16, 12);
        const vertices = [];
        const faces = [];
        const uvs = [];
        
        const positions = geometry.attributes.position.array;
        const indices = geometry.index.array;
        const uvArray = geometry.attributes.uv.array;
        
        // Converti in formato compatibile
        for (let i = 0; i < positions.length; i += 3) {
            vertices.push(new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]));
        }
        
        for (let i = 0; i < indices.length; i += 3) {
            faces.push([indices[i], indices[i + 1], indices[i + 2]]);
        }
        
        for (let i = 0; i < uvArray.length; i += 2) {
            uvs.push(uvArray[i], uvArray[i + 1]);
        }
        
        // Deformazioni facciali
        vertices.forEach((vertex, index) => {
            // Definizione del viso
            if (vertex.z > 0.2) {
                vertex.z += 0.1; // Fronte prominente
            }
            if (vertex.y < -0.1 && vertex.z > 0.1) {
                vertex.z += 0.05; // Mento
            }
        });
        
        return { vertices, faces, uvs };
    }
    
    // Crea braccio con articolazioni
    createArm() {
        const vertices = [];
        const faces = [];
        const uvs = [];
        
        // Segmenti del braccio: spalla, braccio, avambraccio, mano
        const segments = [
            { length: 0.3, radius: 0.15, taper: 0.9 },  // Spalla
            { length: 0.8, radius: 0.12, taper: 0.8 },  // Braccio
            { length: 0.7, radius: 0.10, taper: 0.7 },  // Avambraccio
            { length: 0.2, radius: 0.08, taper: 0.5 }   // Mano
        ];
        
        let currentY = 0;
        const radialSegments = 8;
        
        segments.forEach((segment, segIndex) => {
            const startY = currentY;
            const endY = currentY - segment.length;
            
            // Vertici per questo segmento
            for (let i = 0; i <= 4; i++) {
                const t = i / 4;
                const y = startY + (endY - startY) * t;
                const radius = segment.radius * (1 - t * (1 - segment.taper));
                
                for (let j = 0; j < radialSegments; j++) {
                    const angle = (j / radialSegments) * Math.PI * 2;
                    const x = Math.cos(angle) * radius;
                    const z = Math.sin(angle) * radius;
                    
                    vertices.push(new THREE.Vector3(x, y, z));
                    uvs.push(j / radialSegments, t);
                }
            }
            
            currentY = endY;
        });
        
        // Genera facce
        let vertexIndex = 0;
        segments.forEach((segment, segIndex) => {
            for (let i = 0; i < 4; i++) {
                for (let j = 0; j < radialSegments; j++) {
                    const a = vertexIndex + i * radialSegments + j;
                    const b = vertexIndex + i * radialSegments + ((j + 1) % radialSegments);
                    const c = vertexIndex + (i + 1) * radialSegments + ((j + 1) % radialSegments);
                    const d = vertexIndex + (i + 1) * radialSegments + j;
                    
                    faces.push([a, b, c], [a, c, d]);
                }
            }
            vertexIndex += 5 * radialSegments;
        });
        
        return { vertices, faces, uvs };
    }
    
    // Crea gamba
    createLeg() {
        // Simile al braccio ma con proporzioni diverse
        const vertices = [];
        const faces = [];
        const uvs = [];
        
        const segments = [
            { length: 0.9, radius: 0.18, taper: 0.8 },  // Coscia
            { length: 0.8, radius: 0.12, taper: 0.9 },  // Polpaccio
            { length: 0.3, radius: 0.10, taper: 0.6 }   // Piede
        ];
        
        let currentY = 0;
        const radialSegments = 8;
        
        segments.forEach((segment, segIndex) => {
            const startY = currentY;
            const endY = currentY - segment.length;
            
            for (let i = 0; i <= 4; i++) {
                const t = i / 4;
                const y = startY + (endY - startY) * t;
                const radius = segment.radius * (1 - t * (1 - segment.taper));
                
                for (let j = 0; j < radialSegments; j++) {
                    const angle = (j / radialSegments) * Math.PI * 2;
                    const x = Math.cos(angle) * radius;
                    const z = Math.sin(angle) * radius;
                    
                    vertices.push(new THREE.Vector3(x, y, z));
                    uvs.push(j / radialSegments, t);
                }
            }
            
            currentY = endY;
        });
        
        // Genera facce (stesso algoritmo del braccio)
        let vertexIndex = 0;
        segments.forEach((segment, segIndex) => {
            for (let i = 0; i < 4; i++) {
                for (let j = 0; j < radialSegments; j++) {
                    const a = vertexIndex + i * radialSegments + j;
                    const b = vertexIndex + i * radialSegments + ((j + 1) % radialSegments);
                    const c = vertexIndex + (i + 1) * radialSegments + ((j + 1) % radialSegments);
                    const d = vertexIndex + (i + 1) * radialSegments + j;
                    
                    faces.push([a, b, c], [a, c, d]);
                }
            }
            vertexIndex += 5 * radialSegments;
        });
        
        return { vertices, faces, uvs };
    }
    
    // Versioni divine delle geometrie (più elaborate)
    createDivineTorso() {
        const base = this.createTorso();
        // Aggiungi dettagli divini: muscoli più definiti, proporzioni eroiche
        base.vertices.forEach(vertex => {
            vertex.multiplyScalar(1.2); // Scala maggiore
            // Aggiungi definizione muscolare
            const muscleDef = Math.sin(vertex.x * 3) * Math.cos(vertex.y * 2) * 0.05;
            vertex.add(new THREE.Vector3(muscleDef, 0, muscleDef * 0.5));
        });
        return base;
    }
    
    createDivineHead() {
        const base = this.createHead();
        // Aggiungi aureola e caratteristiche divine
        base.vertices.forEach(vertex => {
            vertex.multiplyScalar(1.1);
            // Fronte più alta e nobile
            if (vertex.y > 0.2) {
                vertex.y += 0.1;
            }
        });
        return base;
    }
    
    createDivineArm() {
        const base = this.createArm();
        // Bracci più possenti
        base.vertices.forEach(vertex => {
            vertex.multiplyScalar(1.15);
        });
        return base;
    }
    
    createDivineRobe() {
        const vertices = [];
        const faces = [];
        const uvs = [];
        
        // Veste fluida con pieghe dinamiche
        const segments = 32;
        const rings = 20;
        
        for (let i = 0; i < rings; i++) {
            const y = -i * 0.2;
            const radius = 1.5 + Math.sin(i * 0.5) * 0.3;
            
            for (let j = 0; j < segments; j++) {
                const angle = (j / segments) * Math.PI * 2;
                const x = Math.cos(angle) * radius;
                const z = Math.sin(angle) * radius;
                
                // Pieghe procedurali
                const fold = Math.sin(angle * 6 + i * 0.3) * 0.1;
                
                vertices.push(new THREE.Vector3(x + fold, y, z + fold));
                uvs.push(j / segments, i / rings);
            }
        }
        
        // Genera facce
        for (let i = 0; i < rings - 1; i++) {
            for (let j = 0; j < segments; j++) {
                const a = i * segments + j;
                const b = i * segments + ((j + 1) % segments);
                const c = (i + 1) * segments + ((j + 1) % segments);
                const d = (i + 1) * segments + j;
                
                faces.push([a, b, c], [a, c, d]);
            }
        }
        
        return { vertices, faces, uvs };
    }
    
    // Utility per combinare geometrie
    addGeometryData(geomData, positions, normals, uvs, indices, vertexOffset, transform = null) {
        geomData.vertices.forEach(vertex => {
            if (transform) {
                vertex.applyMatrix4(transform);
            }
            positions.push(vertex.x, vertex.y, vertex.z);
            normals.push(0, 1, 0); // Placeholder, verrà ricalcolato
        });
        
        geomData.faces.forEach(face => {
            indices.push(
                face[0] + vertexOffset,
                face[1] + vertexOffset,
                face[2] + vertexOffset
            );
        });
        
        geomData.uvs.forEach(uv => {
            uvs.push(uv);
        });
    }
    
    // Deformazioni organiche
    applyOrganicDeformation(positions, normals) {
        for (let i = 0; i < positions.length; i += 3) {
            const x = positions[i];
            const y = positions[i + 1];
            const z = positions[i + 2];
            
            // Noise organico
            const noise = this.noise3D(x * this.noiseScale, y * this.noiseScale, z * this.noiseScale);
            const displacement = noise * 0.05;
            
            positions[i] += displacement;
            positions[i + 1] += displacement * 0.5;
            positions[i + 2] += displacement;
        }
    }
    
    // Deformazioni divine (più fluide)
    applyDivineDeformation(positions, normals) {
        for (let i = 0; i < positions.length; i += 3) {
            const x = positions[i];
            const y = positions[i + 1];
            const z = positions[i + 2];
            
            // Movimento fluido divino
            const wave = Math.sin(x * 0.5) * Math.cos(y * 0.3) * Math.sin(z * 0.4);
            const displacement = wave * 0.03;
            
            positions[i] += displacement;
            positions[i + 1] += displacement * 0.3;
            positions[i + 2] += displacement * 0.7;
        }
    }
    
    // Funzione noise 3D semplificata
    noise3D(x, y, z) {
        return (Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453) % 1;
    }

    // Ottieni posizione del dito di Adamo per il sistema particellare
    getAdamFingerPosition() {
        return new THREE.Vector3(-2.5, 0.5, 1.0);
    }

    // Ottieni posizione del dito di Dio per il sistema particellare
    getGodFingerPosition() {
        return new THREE.Vector3(2.8, 0.8, 1.2);
    }
}
