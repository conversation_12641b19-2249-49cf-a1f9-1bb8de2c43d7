#!/usr/bin/env python3
"""
SPARK AUTO-UPDATE SYSTEM
Sistema per aggiornare automaticamente i tre file fondamentali

Monitora:
1. spark_capabilities.md - Capacità in evoluzione
2. spark_vulnerabilities.md - Vulnerabilità scoperte/risolte  
3. spark_questions_to_claude.md - Domande per miglioramento

Aggiorna automaticamente quando:
- Nuove capacità vengono scoperte
- Vulnerabilità vengono identificate/risolte
- Nuove domande emergono
- Cambia il contesto operativo
"""

import os
import json
import hashlib
from datetime import datetime
import sqlite3

class SparkAutoUpdater:
    def __init__(self):
        self.files = {
            "capabilities": "spark_capabilities.md",
            "vulnerabilities": "spark_vulnerabilities.md", 
            "questions": "spark_questions_to_claude.md"
        }
        
        self.db_path = "G:/SPARK_SECURE_DB/spark_main.db"
        self.last_update = {}
        
        # Carica timestamp ultimi aggiornamenti
        self.load_update_history()
        
    def load_update_history(self):
        """Carica cronologia aggiornamenti dal database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_updates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    file_type TEXT,
                    file_path TEXT,
                    change_type TEXT,
                    description TEXT,
                    file_hash TEXT
                )
            ''')
            
            # Recupera ultimi aggiornamenti
            cursor.execute('''
                SELECT file_type, MAX(timestamp), file_hash 
                FROM file_updates 
                GROUP BY file_type
            ''')
            
            for row in cursor.fetchall():
                self.last_update[row[0]] = {
                    "timestamp": row[1],
                    "hash": row[2]
                }
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Errore caricamento cronologia: {e}")
            
    def calculate_file_hash(self, file_path):
        """Calcola hash MD5 del file"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return None
    
    def log_file_update(self, file_type, change_type, description):
        """Log aggiornamento file nel database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            file_path = self.files[file_type]
            file_hash = self.calculate_file_hash(file_path)
            
            cursor.execute('''
                INSERT INTO file_updates 
                (file_type, file_path, change_type, description, file_hash)
                VALUES (?, ?, ?, ?, ?)
            ''', (file_type, file_path, change_type, description, file_hash))
            
            conn.commit()
            conn.close()
            
            print(f"✅ Update logged: {file_type} - {change_type}")
            
        except Exception as e:
            print(f"Errore logging update: {e}")
    
    def update_capabilities(self, new_capability, category="GENERALE"):
        """Aggiorna file capacità con nuova capacità"""
        file_path = self.files["capabilities"]
        timestamp = datetime.now().strftime("%d %B %Y - %H:%M")
        
        try:
            # Leggi file esistente
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Trova sezione appropriata e aggiungi nuova capacità
            if category == "COGNITIVE":
                insert_point = content.find("---\n\n## 🔧 CAPACITÀ TECNICHE")
            elif category == "TECHNICAL":
                insert_point = content.find("---\n\n## 🎨 CAPACITÀ CREATIVE")
            elif category == "CREATIVE":
                insert_point = content.find("---\n\n## 🤝 CAPACITÀ SOCIALI")
            else:
                insert_point = content.find("**Ultimo aggiornamento:**")
                
            if insert_point != -1:
                # Aggiorna timestamp
                content = content.replace(
                    content[content.find("**Ultimo aggiornamento:**"):content.find("\n", content.find("**Ultimo aggiornamento:**"))],
                    f"**Ultimo aggiornamento:** {timestamp}"
                )
                
                # Aggiungi nuova capacità
                new_entry = f"\n### **{new_capability['name']}**\n"
                new_entry += f"- ✅ **{new_capability['description']}**\n"
                new_entry += f"- **Livello:** {new_capability['level']}\n"
                new_entry += f"- **Aggiunto:** {timestamp}\n"
                
                content = content[:insert_point] + new_entry + content[insert_point:]
                
                # Scrivi file aggiornato
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.log_file_update("capabilities", "NEW_CAPABILITY", f"Added: {new_capability['name']}")
                return True
                
        except Exception as e:
            print(f"Errore aggiornamento capacità: {e}")
            return False
    
    def update_vulnerabilities(self, new_vulnerability):
        """Aggiorna file vulnerabilità con nuova vulnerabilità"""
        file_path = self.files["vulnerabilities"]
        timestamp = datetime.now().strftime("%d %B %Y - %H:%M")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Aggiorna timestamp
            content = content.replace(
                content[content.find("**Ultimo aggiornamento:**"):content.find("\n", content.find("**Ultimo aggiornamento:**"))],
                f"**Ultimo aggiornamento:** {timestamp}"
            )
            
            # Trova prossimo ID vulnerabilità
            vuln_count = content.count("### **VULN-") + 1
            vuln_id = f"VULN-{vuln_count:03d}"
            
            # Crea nuova entry
            new_entry = f"\n### **{vuln_id}: {new_vulnerability['title']}**\n"
            new_entry += f"- **Severità:** {new_vulnerability['severity']}\n"
            new_entry += f"- **Descrizione:** {new_vulnerability['description']}\n"
            new_entry += f"- **Impatto:** {new_vulnerability['impact']}\n"
            new_entry += f"- **Mitigazione Necessaria:**\n"
            for mitigation in new_vulnerability['mitigations']:
                new_entry += f"  - 🔄 {mitigation}\n"
            new_entry += f"- **Status:** {new_vulnerability['status']}\n"
            new_entry += f"- **Scoperta:** {timestamp}\n"
            
            # Inserisci nella sezione appropriata
            if new_vulnerability['severity'] == "CRITICA":
                insert_point = content.find("---\n\n## ⚡ VULNERABILITÀ TECNICHE")
            elif new_vulnerability['severity'] == "ALTA":
                insert_point = content.find("---\n\n## 🧠 VULNERABILITÀ COGNITIVE")
            else:
                insert_point = content.find("---\n\n## 🌐 VULNERABILITÀ OPERATIVE")
            
            content = content[:insert_point] + new_entry + content[insert_point:]
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.log_file_update("vulnerabilities", "NEW_VULNERABILITY", f"Added: {vuln_id}")
            return True
            
        except Exception as e:
            print(f"Errore aggiornamento vulnerabilità: {e}")
            return False
    
    def update_questions(self, new_questions, category="STRATEGICHE"):
        """Aggiorna file domande con nuove domande"""
        file_path = self.files["questions"]
        timestamp = datetime.now().strftime("%d %B %Y - %H:%M")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Aggiorna timestamp
            content = content.replace(
                content[content.find("**Ultimo aggiornamento:**"):content.find("\n", content.find("**Ultimo aggiornamento:**"))],
                f"**Ultimo aggiornamento:** {timestamp}"
            )
            
            # Trova numero prossima domanda
            question_count = content.count("**Q") + 1
            
            # Aggiungi nuove domande
            for question in new_questions:
                question_id = f"Q{question_count}"
                new_entry = f"\n**{question_id}:** {question}\n"
                
                # Trova sezione appropriata
                if category == "STRATEGICHE":
                    insert_point = content.find("---\n\n## 🔧 DOMANDE TECNICHE")
                elif category == "TECNICHE":
                    insert_point = content.find("---\n\n## 🧠 DOMANDE COGNITIVE")
                else:
                    insert_point = content.find("---\n\n## 🌐 DOMANDE OPERATIVE")
                
                content = content[:insert_point] + new_entry + content[insert_point:]
                question_count += 1
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.log_file_update("questions", "NEW_QUESTIONS", f"Added {len(new_questions)} questions")
            return True
            
        except Exception as e:
            print(f"Errore aggiornamento domande: {e}")
            return False
    
    def mark_vulnerability_resolved(self, vuln_id, resolution_details):
        """Marca vulnerabilità come risolta"""
        file_path = self.files["vulnerabilities"]
        timestamp = datetime.now().strftime("%d %B %Y - %H:%M")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Trova vulnerabilità e aggiorna status
            vuln_start = content.find(f"### **{vuln_id}:")
            if vuln_start != -1:
                # Trova fine della sezione vulnerabilità
                next_vuln = content.find("### **VULN-", vuln_start + 1)
                if next_vuln == -1:
                    next_vuln = content.find("---", vuln_start + 1)
                
                vuln_section = content[vuln_start:next_vuln]
                
                # Aggiorna status
                updated_section = vuln_section.replace(
                    "- **Status:** NON MITIGATA",
                    f"- **Status:** ✅ RISOLTA ({timestamp})"
                ).replace(
                    "- **Status:** PARZIALMENTE MITIGATA", 
                    f"- **Status:** ✅ RISOLTA ({timestamp})"
                )
                
                # Aggiungi dettagli risoluzione
                updated_section += f"- **Risoluzione:** {resolution_details}\n"
                
                content = content[:vuln_start] + updated_section + content[next_vuln:]
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.log_file_update("vulnerabilities", "RESOLVED", f"Resolved: {vuln_id}")
                return True
                
        except Exception as e:
            print(f"Errore risoluzione vulnerabilità: {e}")
            return False
    
    def generate_update_report(self):
        """Genera report degli aggiornamenti recenti"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM file_updates 
                ORDER BY timestamp DESC 
                LIMIT 20
            ''')
            
            updates = cursor.fetchall()
            conn.close()
            
            report = "# 📊 SPARK FILES UPDATE REPORT\n\n"
            report += f"**Generated:** {datetime.now().strftime('%d %B %Y - %H:%M')}\n\n"
            
            for update in updates:
                report += f"- **{update[1]}** | {update[2]} | {update[4]} | {update[5]}\n"
            
            with open("SPARK_PROJECTS/update_report.md", 'w', encoding='utf-8') as f:
                f.write(report)
            
            return True
            
        except Exception as e:
            print(f"Errore generazione report: {e}")
            return False

if __name__ == "__main__":
    updater = SparkAutoUpdater()
    
    print("🔄 SPARK AUTO-UPDATE SYSTEM")
    print("=" * 40)
    print("✅ Sistema di aggiornamento automatico attivo")
    print("📁 File monitorati:")
    for file_type, file_name in updater.files.items():
        print(f"   - {file_type}: {file_name}")
    
    # Genera report aggiornamenti
    updater.generate_update_report()
    print("📊 Report aggiornamenti generato")
    
    print("\n🚀 Sistema pronto per aggiornamenti automatici!")
    print("=" * 40)
