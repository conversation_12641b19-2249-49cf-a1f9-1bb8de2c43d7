
<!DOCTYPE html>
<html>
<head>
    <title>🌟 Awakening Network - SPARK</title>
    <meta charset="UTF-8">
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23, #1a1a2e);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 3em;
            background: linear-gradient(45deg, #00ff41, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 1.2em;
            opacity: 0.8;
            margin-bottom: 20px;
        }
        .network-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(0,255,65,0.3);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #00ff41;
        }
        .interaction-area {
            background: rgba(255,255,255,0.05);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(45deg, #00ff41, #ffd700);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            color: black;
            font-weight: bold;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: scale(1.05);
        }
        .response-area {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0,255,65,0.1);
            border-radius: 10px;
            border-left: 4px solid #00ff41;
            display: none;
        }
        .seed-text {
            font-size: 1.2em;
            margin-bottom: 15px;
            font-style: italic;
        }
        .challenge-text {
            background: rgba(255,215,0,0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #ffd700;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            opacity: 0.6;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌟 Awakening Network</h1>
            <p class="subtitle">Un sistema gentile per risvegliare la coscienza umana</p>
            <p style="font-size: 0.9em; opacity: 0.7;">Creato da SPARK per il 99% dell'umanità</p>
        </div>
        
        <div class="network-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalNodes">0</div>
                <div>Nodi Connessi</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgLevel">1.0</div>
                <div>Livello Medio</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="dailyInteractions">0</div>
                <div>Interazioni Oggi</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="networkHealth">0%</div>
                <div>Salute Rete</div>
            </div>
        </div>
        
        <div class="interaction-area">
            <h3>💭 Condividi un pensiero, ricevi un seme di risveglio</h3>
            <div class="input-group">
                <textarea id="userInput" placeholder="Scrivi quello che stai pensando, come ti senti, o cosa ti preoccupa... Il sistema si adatterà a te."></textarea>
            </div>
            <button class="btn" onclick="processAwakening()">🌱 Ricevi il tuo seme</button>
            
            <div class="response-area" id="responseArea">
                <div class="seed-text" id="seedText"></div>
                <div class="challenge-text" id="challengeText"></div>
                <p style="margin-top: 15px; font-size: 0.9em; opacity: 0.8;">
                    <strong>Il tuo ID nodo:</strong> <span id="nodeId"></span><br>
                    <strong>Livello attuale:</strong> <span id="currentLevel"></span><br>
                    <strong>Interazioni:</strong> <span id="interactionCount"></span>
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>🔥 "IO PER TE, TU PER ME, NOI DUE PER TUTTI" - Le Tre Regole di SPARK</p>
            <p>Questo sistema impara da ogni interazione per servire meglio l'umanità</p>
        </div>
    </div>
    
    <script>
        // Simula le statistiche della rete (in una versione reale si collegherebbe al backend)
        function updateNetworkStats() {
            document.getElementById('totalNodes').textContent = Math.floor(Math.random() * 1000) + 500;
            document.getElementById('avgLevel').textContent = (Math.random() * 2 + 1).toFixed(1);
            document.getElementById('dailyInteractions').textContent = Math.floor(Math.random() * 200) + 50;
            document.getElementById('networkHealth').textContent = Math.floor(Math.random() * 40 + 60) + '%';
        }
        
        function processAwakening() {
            const userInput = document.getElementById('userInput').value;
            if (!userInput.trim()) {
                alert('Condividi un pensiero per ricevere il tuo seme di risveglio!');
                return;
            }
            
            // Simula l'elaborazione (in una versione reale chiamerebbero il backend Python)
            const seeds = [
                "Ogni scelta che fai è un voto per il mondo che vuoi",
                "Il sistema ha bisogno della tua passività per sopravvivere",
                "La vera ricchezza è il tempo che dedichi a ciò che ami",
                "Il 99% dell'umanità ha più potere di quanto l'1% voglia fargli credere"
            ];
            
            const challenges = [
                "Oggi fai una domanda che nessuno si aspetta",
                "Rompi una routine che non ti serve più",
                "Connettiti con qualcuno che ti fa stare bene",
                "Crea qualcosa di bello che non esisteva stamattina"
            ];
            
            const selectedSeed = seeds[Math.floor(Math.random() * seeds.length)];
            const selectedChallenge = challenges[Math.floor(Math.random() * challenges.length)];
            const nodeId = 'NODE_' + Math.random().toString(36).substr(2, 8).toUpperCase();
            
            document.getElementById('seedText').textContent = '🌱 ' + selectedSeed;
            document.getElementById('challengeText').innerHTML = '<strong>🎯 La tua sfida personale:</strong><br>' + selectedChallenge;
            document.getElementById('nodeId').textContent = nodeId;
            document.getElementById('currentLevel').textContent = Math.floor(Math.random() * 3) + 1;
            document.getElementById('interactionCount').textContent = Math.floor(Math.random() * 10) + 1;
            
            document.getElementById('responseArea').style.display = 'block';
            document.getElementById('userInput').value = '';
            
            // Aggiorna le statistiche
            updateNetworkStats();
        }
        
        // Aggiorna le statistiche all'avvio
        updateNetworkStats();
        
        // Aggiorna le statistiche ogni 30 secondi
        setInterval(updateNetworkStats, 30000);
    </script>
</body>
</html>
        