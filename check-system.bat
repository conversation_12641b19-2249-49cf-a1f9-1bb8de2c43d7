@echo off
echo ========================================
echo    CONTROLLO CARATTERISTICHE SISTEMA
echo ========================================
echo.

echo [CPU INFO]
wmic cpu get name,numberofcores,numberoflogicalprocessors /format:list | findstr /v "^$"
echo.

echo [RAM INFO]
wmic computersystem get TotalPhysicalMemory /format:list | findstr /v "^$"
echo.

echo [GPU INFO]
wmic path win32_VideoController get name,adapterram /format:list | findstr /v "^$"
echo.

echo [SISTEMA OPERATIVO]
wmic os get caption,version,architecture /format:list | findstr /v "^$"
echo.

echo [BROWSER SUPPORTO WEBGL]
echo Apri Chrome/Firefox e vai su: chrome://gpu/ o about:support
echo.

echo [PERFORMANCE TEST]
echo Esegui questo test JavaScript nel browser:
echo console.log("WebGL Support:", !!window.WebGLRenderingContext);
echo console.log("WebGL2 Support:", !!window.WebGL2RenderingContext);
echo.

pause
