// Shader per post-processing cinematografico avanzato
const FilmGrainShader = {
    uniforms: {
        'tDiffuse': { value: null },
        'time': { value: 0.0 },
        'nIntensity': { value: 0.5 },
        'sIntensity': { value: 0.05 },
        'sCount': { value: 4096 },
        'grayscale': { value: 1 }
    },
    
    vertexShader: `
        varying vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
    
    fragmentShader: `
        uniform float time;
        uniform bool grayscale;
        uniform float nIntensity;
        uniform float sIntensity;
        uniform float sCount;
        uniform sampler2D tDiffuse;
        varying vec2 vUv;
        
        float rand(vec2 co) {
            return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
        }
        
        void main() {
            vec4 cTextureScreen = texture2D(tDiffuse, vUv);
            float dx = rand(vUv + time);
            vec3 cResult = cTextureScreen.rgb + cTextureScreen.rgb * clamp(0.1 + dx, 0.0, 1.0);
            vec2 sc = vec2(sin(vUv.y * sCount), cos(vUv.y * sCount));
            cResult += cTextureScreen.rgb * vec3(sc.x, sc.y, sc.x) * sIntensity;
            cResult = cTextureScreen.rgb + clamp(nIntensity, 0.0, 1.0) * (cResult - cTextureScreen.rgb);
            
            if (grayscale) {
                cResult = vec3(cResult.r * 0.3 + cResult.g * 0.59 + cResult.b * 0.11);
            }
            
            gl_FragColor = vec4(cResult, cTextureScreen.a);
        }
    `
};

const VignetteShader = {
    uniforms: {
        'tDiffuse': { value: null },
        'offset': { value: 1.0 },
        'darkness': { value: 1.0 }
    },
    
    vertexShader: `
        varying vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
    
    fragmentShader: `
        uniform float offset;
        uniform float darkness;
        uniform sampler2D tDiffuse;
        varying vec2 vUv;
        
        void main() {
            vec4 texel = texture2D(tDiffuse, vUv);
            vec2 uv = (vUv - vec2(0.5)) * vec2(offset);
            gl_FragColor = vec4(mix(texel.rgb, vec3(1.0 - darkness), dot(uv, uv)), texel.a);
        }
    `
};

const ColorGradingShader = {
    uniforms: {
        'tDiffuse': { value: null },
        'shadows': { value: new THREE.Vector3(1.0, 1.0, 1.0) },
        'midtones': { value: new THREE.Vector3(1.0, 1.0, 1.0) },
        'highlights': { value: new THREE.Vector3(1.0, 1.0, 1.0) },
        'shadowsStart': { value: 0.0 },
        'shadowsEnd': { value: 0.3 },
        'highlightsStart': { value: 0.55 },
        'highlightsEnd': { value: 1.0 },
        'contrast': { value: 1.0 },
        'brightness': { value: 0.0 },
        'saturation': { value: 1.0 }
    },
    
    vertexShader: `
        varying vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
    
    fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform vec3 shadows;
        uniform vec3 midtones;
        uniform vec3 highlights;
        uniform float shadowsStart;
        uniform float shadowsEnd;
        uniform float highlightsStart;
        uniform float highlightsEnd;
        uniform float contrast;
        uniform float brightness;
        uniform float saturation;
        varying vec2 vUv;
        
        vec3 rgb2hsv(vec3 c) {
            vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
            vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
            vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
            
            float d = q.x - min(q.w, q.y);
            float e = 1.0e-10;
            return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
        }
        
        vec3 hsv2rgb(vec3 c) {
            vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
            vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
            return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
        }
        
        void main() {
            vec4 texel = texture2D(tDiffuse, vUv);
            vec3 color = texel.rgb;
            
            // Luminanza
            float luma = dot(color, vec3(0.299, 0.587, 0.114));
            
            // Color grading per zone
            vec3 shadowsWeight = vec3(1.0 - smoothstep(shadowsStart, shadowsEnd, luma));
            vec3 highlightsWeight = vec3(smoothstep(highlightsStart, highlightsEnd, luma));
            vec3 midtonesWeight = vec3(1.0) - shadowsWeight - highlightsWeight;
            
            color = color * shadows * shadowsWeight +
                    color * midtones * midtonesWeight +
                    color * highlights * highlightsWeight;
            
            // Contrasto e luminosità
            color = (color - 0.5) * contrast + 0.5 + brightness;
            
            // Saturazione
            vec3 hsv = rgb2hsv(color);
            hsv.y *= saturation;
            color = hsv2rgb(hsv);
            
            gl_FragColor = vec4(color, texel.a);
        }
    `
};

const GodRaysShader = {
    uniforms: {
        'tDiffuse': { value: null },
        'tDepth': { value: null },
        'lightPosition': { value: new THREE.Vector2(0.5, 0.5) },
        'exposure': { value: 0.58 },
        'decay': { value: 0.93 },
        'density': { value: 0.96 },
        'weight': { value: 0.4 },
        'samples': { value: 100 }
    },
    
    vertexShader: `
        varying vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
    
    fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform sampler2D tDepth;
        uniform vec2 lightPosition;
        uniform float exposure;
        uniform float decay;
        uniform float density;
        uniform float weight;
        uniform int samples;
        varying vec2 vUv;
        
        void main() {
            vec2 deltaTextCoord = vec2(vUv - lightPosition);
            vec2 textCoo = vUv;
            deltaTextCoord *= 1.0 / float(samples) * density;
            float illuminationDecay = 1.0;
            
            vec4 color = texture2D(tDiffuse, textCoo);
            
            for (int i = 0; i < 100; i++) {
                if (i >= samples) break;
                
                textCoo -= deltaTextCoord;
                vec4 sample = texture2D(tDiffuse, textCoo);
                sample *= illuminationDecay * weight;
                color += sample;
                illuminationDecay *= decay;
            }
            
            gl_FragColor = color * exposure;
        }
    `
};
