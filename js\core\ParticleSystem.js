// Sistema particellare avanzato per l'energia divina
class ParticleSystem {
    constructor(scene, geometryGenerator) {
        this.scene = scene;
        this.geometryGenerator = geometryGenerator;
        this.particles = [];
        this.particleCount = 50000;
        this.time = 0;
        
        // Posizioni delle dita
        this.godFinger = this.geometryGenerator.getGodFingerPosition();
        this.adamFinger = this.geometryGenerator.getAdamFingerPosition();
        
        this.initializeParticles();
        this.createParticleMesh();
    }
    
    initializeParticles() {
        const positions = new Float32Array(this.particleCount * 3);
        const velocities = new Float32Array(this.particleCount * 3);
        const sizes = new Float32Array(this.particleCount);
        const lives = new Float32Array(this.particleCount);
        const energies = new Float32Array(this.particleCount);
        const startPositions = new Float32Array(this.particleCount * 3);
        
        for (let i = 0; i < this.particleCount; i++) {
            const i3 = i * 3;
            
            // Posizione iniziale: area tra le dita
            const t = Math.random();
            const godPos = this.godFinger.clone();
            const adamPos = this.adamFinger.clone();
            const pos = godPos.lerp(adamPos, t);
            
            // Aggiungi dispersione
            pos.x += (Math.random() - 0.5) * 2.0;
            pos.y += (Math.random() - 0.5) * 1.5;
            pos.z += (Math.random() - 0.5) * 1.0;
            
            positions[i3] = pos.x;
            positions[i3 + 1] = pos.y;
            positions[i3 + 2] = pos.z;
            
            startPositions[i3] = pos.x;
            startPositions[i3 + 1] = pos.y;
            startPositions[i3 + 2] = pos.z;
            
            // Velocità iniziale
            velocities[i3] = (Math.random() - 0.5) * 0.1;
            velocities[i3 + 1] = (Math.random() - 0.5) * 0.1;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;
            
            // Proprietà particella
            sizes[i] = Math.random() * 0.5 + 0.2;
            lives[i] = Math.random();
            energies[i] = Math.random() * 0.8 + 0.2;
        }
        
        this.particleGeometry = new THREE.BufferGeometry();
        this.particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        this.particleGeometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        this.particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        this.particleGeometry.setAttribute('life', new THREE.BufferAttribute(lives, 1));
        this.particleGeometry.setAttribute('energy', new THREE.BufferAttribute(energies, 1));
        this.particleGeometry.setAttribute('startPosition', new THREE.BufferAttribute(startPositions, 3));
    }
    
    createParticleMesh() {
        // Crea texture per le particelle
        this.createParticleTexture();
        
        // Material con shader personalizzato
        this.particleMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                deltaTime: { value: 0.016 },
                godFinger: { value: this.godFinger },
                adamFinger: { value: this.adamFinger },
                creationIntensity: { value: 1.0 },
                particleScale: { value: 1.0 },
                particleTexture: { value: this.particleTexture },
                opacity: { value: 0.8 }
            },
            vertexShader: ParticleShader.vertexShader,
            fragmentShader: ParticleShader.fragmentShader,
            transparent: true,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            vertexColors: true
        });
        
        this.particleMesh = new THREE.Points(this.particleGeometry, this.particleMaterial);
        this.scene.add(this.particleMesh);
    }
    
    createParticleTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');
        
        // Gradiente radiale per particelle
        const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
        gradient.addColorStop(0.3, 'rgba(255, 200, 100, 0.8)');
        gradient.addColorStop(0.7, 'rgba(100, 150, 255, 0.4)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 64, 64);
        
        this.particleTexture = new THREE.CanvasTexture(canvas);
    }
    
    update(deltaTime) {
        this.time += deltaTime;
        
        // Aggiorna uniforms
        this.particleMaterial.uniforms.time.value = this.time;
        this.particleMaterial.uniforms.deltaTime.value = deltaTime;
        this.particleMaterial.uniforms.creationIntensity.value = 
            1.0 + Math.sin(this.time * 2.0) * 0.3;
        
        // Simulazione fisica delle particelle
        this.updateParticlePhysics(deltaTime);
        
        // Rigenera particelle morte
        this.regenerateParticles();
        
        // Aggiorna attributi
        this.particleGeometry.attributes.position.needsUpdate = true;
        this.particleGeometry.attributes.velocity.needsUpdate = true;
        this.particleGeometry.attributes.life.needsUpdate = true;
        this.particleGeometry.attributes.energy.needsUpdate = true;
    }
    
    updateParticlePhysics(deltaTime) {
        const positions = this.particleGeometry.attributes.position.array;
        const velocities = this.particleGeometry.attributes.velocity.array;
        const lives = this.particleGeometry.attributes.life.array;
        const energies = this.particleGeometry.attributes.energy.array;
        
        for (let i = 0; i < this.particleCount; i++) {
            const i3 = i * 3;
            
            // Posizione corrente
            const pos = new THREE.Vector3(
                positions[i3],
                positions[i3 + 1],
                positions[i3 + 2]
            );
            
            // Velocità corrente
            const vel = new THREE.Vector3(
                velocities[i3],
                velocities[i3 + 1],
                velocities[i3 + 2]
            );
            
            // Forze
            const forces = this.calculateForces(pos, energies[i]);
            
            // Integrazione Verlet
            vel.add(forces.multiplyScalar(deltaTime));
            pos.add(vel.clone().multiplyScalar(deltaTime));
            
            // Damping
            vel.multiplyScalar(0.98);
            
            // Aggiorna arrays
            positions[i3] = pos.x;
            positions[i3 + 1] = pos.y;
            positions[i3 + 2] = pos.z;
            
            velocities[i3] = vel.x;
            velocities[i3 + 1] = vel.y;
            velocities[i3 + 2] = vel.z;
            
            // Aggiorna vita
            lives[i] -= deltaTime * 0.2;
            if (lives[i] <= 0) {
                lives[i] = 1.0;
                energies[i] = Math.random() * 0.8 + 0.2;
                
                // Respawn vicino alle dita
                const spawnNearGod = Math.random() < 0.7;
                const spawnPos = spawnNearGod ? this.godFinger : this.adamFinger;
                
                positions[i3] = spawnPos.x + (Math.random() - 0.5) * 0.5;
                positions[i3 + 1] = spawnPos.y + (Math.random() - 0.5) * 0.5;
                positions[i3 + 2] = spawnPos.z + (Math.random() - 0.5) * 0.5;
                
                velocities[i3] = (Math.random() - 0.5) * 0.2;
                velocities[i3 + 1] = (Math.random() - 0.5) * 0.2;
                velocities[i3 + 2] = (Math.random() - 0.5) * 0.2;
            }
        }
    }
    
    calculateForces(position, energy) {
        const forces = new THREE.Vector3();
        
        // Forza di attrazione verso le dita
        const toGod = this.godFinger.clone().sub(position);
        const toAdam = this.adamFinger.clone().sub(position);
        
        const distToGod = toGod.length();
        const distToAdam = toAdam.length();
        
        // Attrazione divina (più forte)
        if (distToGod > 0.1) {
            const godForce = toGod.normalize().multiplyScalar(
                energy * 2.0 / (distToGod * distToGod + 0.1)
            );
            forces.add(godForce);
        }
        
        // Attrazione verso Adamo (più debole)
        if (distToAdam > 0.1) {
            const adamForce = toAdam.normalize().multiplyScalar(
                energy * 0.8 / (distToAdam * distToAdam + 0.1)
            );
            forces.add(adamForce);
        }
        
        // Forza di flusso tra le dita
        const fingerDirection = this.adamFinger.clone().sub(this.godFinger).normalize();
        const flowForce = fingerDirection.multiplyScalar(energy * 0.5);
        forces.add(flowForce);
        
        // Turbulenza
        const turbulence = new THREE.Vector3(
            Math.sin(position.x * 2 + this.time * 3) * 0.3,
            Math.cos(position.y * 1.5 + this.time * 2) * 0.2,
            Math.sin(position.z * 2.5 + this.time * 4) * 0.25
        );
        forces.add(turbulence.multiplyScalar(energy));
        
        // Forza centripeta per movimento a spirale
        const center = this.godFinger.clone().lerp(this.adamFinger, 0.5);
        const toCenter = center.sub(position);
        const distance = toCenter.length();
        
        if (distance > 0.1) {
            const spiral = new THREE.Vector3(
                -toCenter.z,
                toCenter.y * 0.5,
                toCenter.x
            ).normalize().multiplyScalar(energy * 0.3);
            forces.add(spiral);
        }
        
        return forces;
    }
    
    regenerateParticles() {
        // Logica già implementata in updateParticlePhysics
    }
    
    // Controlli per l'interfaccia
    setIntensity(intensity) {
        this.particleMaterial.uniforms.creationIntensity.value = intensity;
    }
    
    setOpacity(opacity) {
        this.particleMaterial.uniforms.opacity.value = opacity;
    }
    
    setScale(scale) {
        this.particleMaterial.uniforms.particleScale.value = scale;
    }
    
    getParticleCount() {
        return this.particleCount;
    }
    
    dispose() {
        this.particleGeometry.dispose();
        this.particleMaterial.dispose();
        this.particleTexture.dispose();
        this.scene.remove(this.particleMesh);
    }
}
