// Sistema di illuminazione cinematografico avanzato
class LightingSystem {
    constructor(scene) {
        this.scene = scene;
        this.lights = {};
        this.time = 0;
        
        this.setupLights();
        this.setupEnvironment();
    }
    
    setupLights() {
        // Luce principale divina (key light)
        this.lights.divine = new THREE.DirectionalLight(0xffd700, 2.5);
        this.lights.divine.position.set(5, 8, 3);
        this.lights.divine.castShadow = true;
        this.lights.divine.shadow.mapSize.width = 4096;
        this.lights.divine.shadow.mapSize.height = 4096;
        this.lights.divine.shadow.camera.near = 0.1;
        this.lights.divine.shadow.camera.far = 50;
        this.lights.divine.shadow.camera.left = -10;
        this.lights.divine.shadow.camera.right = 10;
        this.lights.divine.shadow.camera.top = 10;
        this.lights.divine.shadow.camera.bottom = -10;
        this.lights.divine.shadow.bias = -0.0001;
        this.scene.add(this.lights.divine);
        
        // Luce di riempimento (fill light)
        this.lights.fill = new THREE.DirectionalLight(0x87ceeb, 0.8);
        this.lights.fill.position.set(-3, 4, 2);
        this.scene.add(this.lights.fill);
        
        // Luce di contorno (rim light)
        this.lights.rim = new THREE.DirectionalLight(0xffffff, 1.2);
        this.lights.rim.position.set(-2, 2, -5);
        this.scene.add(this.lights.rim);
        
        // Luce ambientale
        this.lights.ambient = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(this.lights.ambient);
        
        // Luci puntuali per le dita (effetto creazione)
        this.lights.godFinger = new THREE.PointLight(0xffd700, 3.0, 5);
        this.lights.godFinger.position.set(2.8, 0.8, 1.2);
        this.scene.add(this.lights.godFinger);
        
        this.lights.adamFinger = new THREE.PointLight(0xffffff, 1.5, 3);
        this.lights.adamFinger.position.set(-2.5, 0.5, 1.0);
        this.scene.add(this.lights.adamFinger);
        
        // Luce volumetrica per atmosfera
        this.lights.volumetric = new THREE.SpotLight(0xffd700, 2.0, 20, Math.PI / 6, 0.3);
        this.lights.volumetric.position.set(0, 10, 0);
        this.lights.volumetric.target.position.set(0, 0, 0);
        this.lights.volumetric.castShadow = true;
        this.scene.add(this.lights.volumetric);
        this.scene.add(this.lights.volumetric.target);
    }
    
    setupEnvironment() {
        // Skybox procedurale
        const skyGeometry = new THREE.SphereGeometry(100, 32, 32);
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                sunPosition: { value: new THREE.Vector3(5, 8, 3) },
                turbidity: { value: 2.0 },
                rayleigh: { value: 1.0 },
                mieCoefficient: { value: 0.005 },
                mieDirectionalG: { value: 0.8 },
                luminance: { value: 1.0 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform vec3 sunPosition;
                uniform float turbidity;
                uniform float rayleigh;
                uniform float mieCoefficient;
                uniform float mieDirectionalG;
                uniform float luminance;
                
                varying vec3 vWorldPosition;
                
                const float e = 2.71828182845904523536028747135266249775724709369995957;
                const float pi = 3.141592653589793238462643383279502884197169;
                
                const float n = 1.0003;
                const float N = 2.545E25;
                const float pn = 0.035;
                
                const vec3 lambda = vec3(680E-9, 550E-9, 450E-9);
                const vec3 K = vec3(0.686, 0.678, 0.666);
                
                const float v = 4.0;
                const float rayleighZenithLength = 8.4E3;
                const float mieZenithLength = 1.25E3;
                const vec3 up = vec3(0.0, 1.0, 0.0);
                
                const float sunAngularDiameterCos = 0.999956676946448443553574619906976478926848692873900859324;
                
                const float THREE_OVER_SIXTEENPI = 0.05968310365946075;
                const float ONE_OVER_FOURPI = 0.07957747154594767;
                
                float rayleighPhase(float cosTheta) {
                    return THREE_OVER_SIXTEENPI * (1.0 + pow(cosTheta, 2.0));
                }
                
                float hgPhase(float cosTheta, float g) {
                    float g2 = pow(g, 2.0);
                    float inverse = 1.0 / pow(1.0 - 2.0 * g * cosTheta + g2, 1.5);
                    return ONE_OVER_FOURPI * ((1.0 - g2) * inverse);
                }
                
                void main() {
                    vec3 direction = normalize(vWorldPosition);
                    vec3 sunDirection = normalize(sunPosition);
                    
                    float sunE = 1000.0 * luminance;
                    float sunfade = 1.0 - clamp(1.0 - exp((sunPosition.y / 450000.0)), 0.0, 1.0);
                    
                    float rayleighCoefficient = rayleigh - (1.0 * (1.0 - sunfade));
                    vec3 betaR = vec3(5.804542996261093E-6, 1.3562911419845635E-5, 3.0265902468824876E-5) * rayleighCoefficient;
                    
                    vec3 betaM = vec3(0.003996, 0.003996, 0.003996) * mieCoefficient;
                    
                    float zenithAngle = acos(max(0.0, dot(up, direction)));
                    float inverse = 1.0 / (cos(zenithAngle) + 0.15 * pow(93.885 - ((zenithAngle * 180.0) / pi), -1.253));
                    float sR = rayleighZenithLength * inverse;
                    float sM = mieZenithLength * inverse;
                    
                    vec3 Fex = exp(-(betaR * sR + betaM * sM));
                    
                    float cosTheta = dot(direction, sunDirection);
                    
                    float rPhase = rayleighPhase(cosTheta * 0.5 + 0.5);
                    vec3 betaRTheta = betaR * rPhase;
                    
                    float mPhase = hgPhase(cosTheta, mieDirectionalG);
                    vec3 betaMTheta = betaM * mPhase;
                    
                    vec3 Lin = pow(sunE * ((betaRTheta + betaMTheta) / (betaR + betaM)) * (1.0 - Fex), vec3(1.5));
                    Lin *= mix(vec3(1.0), pow(sunE * ((betaRTheta + betaMTheta) / (betaR + betaM)) * Fex, vec3(1.0 / 2.0)), clamp(pow(1.0 - dot(up, sunDirection), 5.0), 0.0, 1.0));
                    
                    vec3 direction_normalized = normalize(direction);
                    float theta = acos(direction_normalized.y);
                    float phi = atan(direction_normalized.z, direction_normalized.x);
                    vec2 uv = vec2(phi, theta) / vec2(2.0 * pi, pi) + vec2(0.5, 0.0);
                    
                    vec3 L0 = vec3(0.1) * Fex;
                    
                    float sundisk = smoothstep(sunAngularDiameterCos, sunAngularDiameterCos + 0.00002, cosTheta);
                    L0 += (sunE * 19000.0 * Fex) * sundisk;
                    
                    vec3 color = (Lin + L0) * 0.04 + vec3(0.0, 0.0003, 0.00075);
                    
                    color = pow(color, vec3(1.0 / (1.2 + (1.2 * sunfade))));
                    
                    // Aggiungi variazioni temporali
                    color += sin(time * 0.5 + phi * 2.0) * 0.02;
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `,
            side: THREE.BackSide
        });
        
        this.skyMesh = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(this.skyMesh);
    }
    
    update(deltaTime) {
        this.time += deltaTime;
        
        // Animazione delle luci
        this.animateLights();
        
        // Aggiorna skybox
        if (this.skyMesh) {
            this.skyMesh.material.uniforms.time.value = this.time;
        }
    }
    
    animateLights() {
        // Pulsazione della luce divina
        const divineIntensity = 2.5 + Math.sin(this.time * 2.0) * 0.5;
        this.lights.divine.intensity = divineIntensity;
        
        // Oscillazione della luce delle dita
        const fingerPulse = Math.sin(this.time * 4.0) * 0.5 + 1.0;
        this.lights.godFinger.intensity = 3.0 * fingerPulse;
        this.lights.adamFinger.intensity = 1.5 * fingerPulse * 0.7;
        
        // Movimento sottile della luce principale
        this.lights.divine.position.x = 5 + Math.sin(this.time * 0.3) * 0.5;
        this.lights.divine.position.y = 8 + Math.cos(this.time * 0.2) * 0.3;
        
        // Variazione colore ambientale
        const hue = (this.time * 0.1) % 1.0;
        const ambientColor = new THREE.Color().setHSL(hue * 0.1 + 0.6, 0.3, 0.2);
        this.lights.ambient.color = ambientColor;
        
        // Effetto breathing per la luce volumetrica
        this.lights.volumetric.intensity = 2.0 + Math.sin(this.time * 1.5) * 0.3;
        this.lights.volumetric.angle = (Math.PI / 6) + Math.sin(this.time * 0.8) * 0.1;
    }
    
    // Controlli per l'interfaccia
    setDivineIntensity(intensity) {
        this.lights.divine.intensity = intensity;
    }
    
    setAmbientIntensity(intensity) {
        this.lights.ambient.intensity = intensity;
    }
    
    setCreationIntensity(intensity) {
        this.lights.godFinger.intensity = 3.0 * intensity;
        this.lights.adamFinger.intensity = 1.5 * intensity;
    }
    
    // Preset di illuminazione
    setPreset(presetName) {
        switch (presetName) {
            case 'divine':
                this.lights.divine.intensity = 3.0;
                this.lights.divine.color.setHex(0xffd700);
                this.lights.ambient.intensity = 0.2;
                break;
                
            case 'dramatic':
                this.lights.divine.intensity = 4.0;
                this.lights.divine.color.setHex(0xff6b35);
                this.lights.ambient.intensity = 0.1;
                this.lights.rim.intensity = 2.0;
                break;
                
            case 'ethereal':
                this.lights.divine.intensity = 2.0;
                this.lights.divine.color.setHex(0x87ceeb);
                this.lights.ambient.intensity = 0.4;
                this.lights.fill.intensity = 1.2;
                break;
                
            case 'renaissance':
                this.lights.divine.intensity = 2.5;
                this.lights.divine.color.setHex(0xffeaa7);
                this.lights.ambient.intensity = 0.3;
                this.lights.rim.intensity = 0.8;
                break;
        }
    }
    
    dispose() {
        // Rimuovi tutte le luci
        Object.values(this.lights).forEach(light => {
            this.scene.remove(light);
            if (light.target) {
                this.scene.remove(light.target);
            }
        });
        
        // Rimuovi skybox
        if (this.skyMesh) {
            this.skyMesh.material.dispose();
            this.skyMesh.geometry.dispose();
            this.scene.remove(this.skyMesh);
        }
    }
}
